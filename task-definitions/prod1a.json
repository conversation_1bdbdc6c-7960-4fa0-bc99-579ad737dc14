{"containerDefinitions": [{"name": "telegramBot-collector", "image": "685767522279.dkr.ecr.eu-west-2.amazonaws.com/base-app-adot-production:latest", "command": ["--config=/etc/ecs/container-insights/custom-adot-config.yaml"], "cpu": 256, "memory": 512, "essential": false, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/otel-collectors-production", "awslogs-region": "eu-west-2", "awslogs-stream-prefix": "ecs", "awslogs-datetime-format": "%Y-%m-%d %H:%M:%S"}}}, {"name": "telegramBot-container", "image": "public.ecr.aws/docker/library/python:3.11", "cpu": 0, "essential": true, "mountPoints": [], "volumesFrom": [], "dependsOn": [{"containerName": "telegramBot-collector", "condition": "START"}], "environment": [{"name": "LOG_LEVEL", "value": "INFO"}, {"name": "TIMESERIES_DATA_VERSION", "value": "v-00004"}, {"name": "LOCAL_RUN", "value": "0"}, {"name": "RESEARCH_VOL_DASHBOARD_LAMBDA_NAME", "value": "arn:aws:lambda:eu-west-2:685767522279:function:ResearchVolDashboardLambdaFunction:prod"}, {"name": "STAGE", "value": "prod"}, {"name": "GLOBAL_SUPPORTED_VOL_CURRENCIES", "value": "[\"BTC\", \"ETH\", \"SOL\", \"XRP\", \"SUI\", \"ARB\", \"OP\"]"}, {"name": "SSM_INDEX_CONFIG_PATH", "value": "/config/price_indices_generated"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/telegramBot-containers-prod1a", "awslogs-region": "eu-west-2", "awslogs-stream-prefix": "ecs", "awslogs-datetime-format": "%Y-%m-%d %H:%M:%S"}}}], "family": "telegramBot-task-definition", "taskRoleArn": "arn:aws:iam::685767522279:role/telegramBotContainerRole", "executionRoleArn": "arn:aws:iam::685767522279:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "ARM64"}}