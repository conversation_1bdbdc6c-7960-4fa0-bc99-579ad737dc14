import asyncio
import logging
import signal
from collections.abc import Callable
from typing import Any

import utils_general

from telegram_bot.configs.aws_config import get_bot_config_ssm_params
from telegram_bot.configs.bot_config import (
    Config,
    get_bot_config,
)
from telegram_bot.runners import BotBuilder
from telegram_bot.typings import WhitelistedChat, WhitelistedUser

utils_general.setup_python_logger(level="INFO")
# set higher logging level for httpx to avoid all GET and POST requests being logged
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)


async def main() -> None:

    cfg = Config()
    ssm_params = get_bot_config_ssm_params()
    utils_general.setup_python_logger(level=cfg.LOG_LEVEL)

    if bool(int(cfg.LOCAL_RUN)):
        logging.info("Running bot in local mode")
        bot_name_to_keys = ssm_params["local_bots"]
    else:
        bot_name_to_keys = ssm_params["bots"]

    logging.info("Finished config initialisation")

    bots = []

    for bot_id, api_key in bot_name_to_keys.items():

        whitelist_per_bot = ssm_params["whitelist"].get(bot_id, {})

        config = get_bot_config(bot_id)
        config["timeseries_version"] = cfg.TIMESERIES_DATA_VERSION
        config["funding_rate_config"] = ssm_params["funding_rate_config"]
        config["spot_config"] = ssm_params["spot_config"]
        config["internal_api_key"] = ssm_params[
            cfg.BLOCKSCHOLES_INTERNAL_API_KEY
        ]
        config["whitelisted_users"] = [
            WhitelistedUser.model_validate(user)
            for user in whitelist_per_bot.get("users", [])
        ]
        config["whitelisted_chats"] = [
            WhitelistedChat.model_validate(chat)
            for chat in whitelist_per_bot.get("chats", [])
        ]
        try:
            bot = BotBuilder(
                aws_stage=cfg.STAGE,
                bot_id=bot_id,
                bot_api_key=api_key,
                config=config,
            )
        except Exception:
            logging.exception(f"Error while building bot {bot_id=}")
            raise

        bots.append(bot)

    tasks = []
    for bot in bots:
        tasks.append(asyncio.create_task(bot.run_polling()))

    await asyncio.gather(*tasks)


def _handle_task_exception(
    loop: asyncio.AbstractEventLoop, context: dict[str, Any]
) -> None:
    exception = context.get("exception")
    message = context.get("message")
    if exception:
        logging.error(f"Uncaught exception: {exception}", exc_info=exception)
    else:
        logging.error(f"Uncaught exception: {message}")
    loop.stop()


def _handle_task_result(task: asyncio.Task[None]) -> None:
    try:
        task.result()
    except asyncio.CancelledError:
        pass  # Task cancellation should not be logged as an error.
    except Exception:
        logging.exception("Exception raised by task = %r", task)
        raise


def create_signal_handler(
    sig: signal.Signals, loop: asyncio.AbstractEventLoop
) -> Callable[[], None]:
    """Return a no-argument callable for add_signal_handler."""

    def handler() -> None:
        task = asyncio.create_task(_signal_handler(sig, loop))
        task.add_done_callback(_handle_task_result)

    return handler


async def _signal_handler(
    signal: signal.Signals, loop: asyncio.AbstractEventLoop
) -> None:
    logging.info("Handling termination signal")
    tasks = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]

    [task.cancel() for task in tasks]

    logging.info(f"Cancelling {len(tasks)} outstanding tasks")
    await asyncio.gather(*tasks, return_exceptions=True)
    loop.stop()


if __name__ == "__main__":

    loop = asyncio.get_event_loop()
    loop.set_exception_handler(_handle_task_exception)
    signals = (signal.SIGTERM, signal.SIGINT)
    for s in signals:
        loop.add_signal_handler(s, create_signal_handler(s, loop))

    main_task = loop.create_task(main())
    main_task.add_done_callback(_handle_task_result)

    logging.info("Event loop started. Running bots...")

    try:
        loop.run_forever()
    finally:
        logging.info("Shutting down")
        loop.close()
