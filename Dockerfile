FROM python:3.11


ARG ACCESS_TOKEN


# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/uv

# Install git and gcc - required to install dependencies from internal repositories
RUN apt-get update && apt-get install -y git gcc

# Change the working directory to the `app` directory
WORKDIR /app

# Set up netrc for GitHub authentication
RUN echo -e "machine github.com\n  login ${ACCESS_TOKEN}" > ~/.netrc
RUN chmod 0600 ~/.netrc


# Install dependencies
RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --frozen --no-install-project

# Copy the project into the image
ADD . /app

# Sync the project
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen

# Run the service by default
CMD ["uv", "run", "./app.py"]