[project]
name = "telegrambot"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aioboto3>=7.0.0",
    "aiobotocore>=2.15.2",
    "boto3-stubs>=1.35.66",
    "datagrabber",
    "matplotlib>=3.10.1",
    "pandas-stubs>=2.2.3.241009",
    "pydantic-settings>=2.6.0",
    "pydantic>=2.9.2",
    "pytest>=8.3.4",
    "python-telegram-bot[job-queue,webhooks]>=21.6",
    "requests>=2.32.3",
    "types-requests>=2.32.0.20241016",
    "utils-aws",
    "utils-general",
    "posthog>=4.2.0",
    "orjson>=3.10.12",
]


[tool.pytest.ini_options]
pythonpath = ["."]

[tool.black]
line-length = 80
target-version = ['py311']
include = '\.pyi?$'
exclude = '(venv/*|env/*)'

[tool.ruff]
line-length = 80
extend-exclude = ['venv', 'env']


[tool.ruff.lint]
select = [
    'F',   # pyflakes
    'E',   # pycodestyle
    'W',   # pycodestyle
    'I',   # isort
    'UP',  # pyupgrade
    'B',   # flake8-bugbear
    'C',   # flake8-comprehensions
    'DTZ', # flake8-datetimez
    'RUF', # ruff
]

ignore = [
    'E501', # line too long, handled by black
    'C901', # complex structure, not needed
    'E712', # Doesn't work correctly with dataframes
]

[tool.ruff.lint.per-file-ignores]
'__init__.py' = [
    'F401', # unused import
    'E402', # module import not at top of file
]

[tool.mypy]
warn_return_any = true
python_version = '3.11'
warn_unused_configs = true
allow_redefinition = false
disallow_untyped_defs = true
no_implicit_optional = true
check_untyped_defs = false
strict = true
plugins = 'pydantic.mypy'

[tool.pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true

[tool.uv]
dev-dependencies = [
    "black>=24.10.0",
    "mypy>=1.14.0",
    "pre-commit>=4.0.1",
    "pytest-asyncio>=0.24.0",
    "pytest>=8.3.3",
    "ruff>=0.8.4",
]

[tool.setuptools.packages.find]
where = "telegram_bot"

[tool.uv.sources]
utils-general = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_general", rev = "v4.1.24" }
utils-aws = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_aws", rev = "v4.1.24" }
datagrabber = { git = "https://github.com/blockscholes/datagrabber", rev = "v1.12.1" }
