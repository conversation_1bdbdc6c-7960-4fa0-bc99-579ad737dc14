name: Slack PR Notification

on:
  pull_request:
    types: [labeled]

jobs:
  notify-slack:
    if: github.repository == 'blockscholes/${{ github.event.repository.name }}'
    runs-on: ubuntu-latest
    steps:
      - name: Join labels
        id: join-labels
        run: |
          PR_LABELS="${{ join(github.event.pull_request.labels.*.name, ' ') }}"
          echo "PR Labels: $PR_LABELS"
          echo "::set-output name=PR_LABELS::$PR_LABELS"
      - name: Notify slack PR
        uses: aurelien-baudet/workflow-dispatch@v2
        with:
          workflow: slack-pr-notification.yml
          repo: blockscholes/workflows
          token: ${{ secrets.DEPLOY_CHAIN_TOKEN }}
          ref: refs/heads/main
          inputs: '{ "pr_labels": "${{ steps.join-labels.outputs.PR_LABELS }}", "pr_url": "${{ github.event.pull_request.html_url }}" }'
