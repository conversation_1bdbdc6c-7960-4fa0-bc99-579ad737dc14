name: Production Stream Deployment
on:
  workflow_dispatch:
    inputs:
      region:
        description: "AWS Region to deploy to"
        type: choice
        default: eu-west-2
        options:
          - eu-west-2
          - eu-west-1
      cpuArchitecture:
        description: "CPU architecture to deploy"
        type: choice
        default: ARM64
        options:
          - X86_64
          - ARM64

  pull_request:
    types:
      - closed
    branches:
      - production_stream

jobs:

  deploy:
    name: Deploy TelegramBot to Production
    uses: blockscholes/workflows/.github/workflows/stream-deploy.yml@main
    with:
      environment: prod
      repository: ${{ github.repository }}
      ref: ${{ github.ref }}
      actor: ${{ github.actor }}
      cpuArchitecture: ${{ inputs.cpuArchitecture || 'ARM64' }}
      region: ${{ inputs.region || 'eu-west-2' }}
      ecr_repository: telegrambot-prod1a
      ecs_service: telegramBot-service
      task_def: task-definitions/prod1a.json
      container: telegramBot-container
      python_version: 3.11
      docker_file: Dockerfile
    secrets: inherit
    
    
