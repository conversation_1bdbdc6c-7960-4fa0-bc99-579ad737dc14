name: Staging Stream Deployment

on:
  workflow_dispatch:
    inputs:
      region:
        description: "AWS Region to deploy to"
        type: choice
        default: eu-west-2
        options:
          - eu-west-2
          - eu-west-1
      cpuArchitecture:
        description: "CPU architecture to deploy"
        type: choice
        default: ARM64
        options:
          - X86_64
          - ARM64
  push:
    branches:
      - main

jobs:
  get-python-version:
    runs-on: arm-runner
    outputs:
      python_version: ${{ steps.get_version.outputs.python_version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Read Python version
        id: get_version
        run: echo "python_version=$(cat .python-version)" >> $GITHUB_OUTPUT

  deploy:
    name: Deploy TelegramBot to Staging
    needs: get-python-version
    uses: blockscholes/workflows/.github/workflows/stream-deploy.yml@main
    with:
      environment: staging
      repository: ${{ github.repository }}
      ref: ${{ github.ref }}
      cpuArchitecture: ${{ inputs.cpuArchitecture || 'ARM64' }}
      region: ${{ inputs.region || 'eu-west-2' }}
      ecr_repository: telegrambot-staging
      ecs_service: telegramBot-service
      task_def: task-definitions/staging.json
      container: telegramBot-container
      python_version: ${{ needs.get-python-version.outputs.python_version }}
      docker_file: Dockerfile
    secrets: inherit
