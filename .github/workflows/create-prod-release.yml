name: Create Production Release
on:
  workflow_dispatch:

jobs:
  productionRelease:
    runs-on: ubuntu-latest
    steps:
      - name: Create production release
        uses: aurelien-baudet/workflow-dispatch@v2
        with:
          workflow: create-prod-release.yml
          repo:  blockscholes/workflows
          token: ${{ secrets.DEPLOY_CHAIN_TOKEN }}
          ref: refs/heads/main
          inputs: '{ "repository": "${{ github.event.repository.name }}" }'
