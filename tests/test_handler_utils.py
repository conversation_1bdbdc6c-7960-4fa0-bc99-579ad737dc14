from datetime import UTC, datetime, timedelta

import pytest

from telegram_bot.handlers.utils import (
    apply_time_delay_to_command,
    generate_random_date_within_cutoff,
    is_valid_lookback_suffix,
    parse_lookback_period,
    should_apply_delay,
)
from telegram_bot.typings import CommandType


@pytest.mark.parametrize(
    (
        "is_premium_bot",
        "user_id",
        "chat_id",
        "user_username",
        "user_whitelist",
        "chat_whitelist",
        "expected",
    ),
    [
        # ── Non-premium bot cases ────────────────────────────────────────────
        # No whitelists hit  → delay
        (False, 1, 10, "alice", [], [], True),
        # user_id whitelisted → no delay
        (False, 1, 10, "alice", [1], [], False),
        # username whitelisted → no delay
        (<PERSON>alse, 1, 10, "alice", ["alice"], [], False),
        # chat_id whitelisted → no delay
        (False, 1, 10, "alice", [], [10], False),
        # user_id and chat_id whitelisted → no delay
        (False, 1, 10, "alice", [1], [10], False),
        # username and chat_id whitelisted → no delay
        (False, 2, 10, "bob", ["bob"], [10], False),
        # username is None; user_id whitelisted → no delay
        (False, 3, 10, None, [3], [], False),
        # username is None; chat_id whitelisted → no delay
        (False, 4, 10, None, [], [10], False),
        # username is None; no whitelists hit → delay
        (False, 5, 10, None, [], [], True),
        # ── Premium bot cases ───────────────────────────────────────────────
        # Premium overrides all → no delay
        (True, 1, 10, "alice", [], [], False),
        # Premium with some whitelists (should still be no delay)
        (True, 2, 20, "bob", [2], [20], False),
    ],
)
def test_should_apply_delay(
    is_premium_bot: bool,
    user_id: int,
    chat_id: int,
    user_username: str | None,
    user_whitelist: list[int | str],
    chat_whitelist: list[int],
    expected: bool,
) -> None:
    assert (
        should_apply_delay(
            is_premium_bot,
            user_id,
            chat_id,
            user_username,
            user_whitelist,
            chat_whitelist,
        )
        is expected
    )


@pytest.mark.parametrize(
    "initial_time, command_type, apply_delay, expected_time",
    [
        (
            datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC),
            "price",
            True,
            datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC) - timedelta(hours=6),
        ),
        (
            datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC),
            "chart",
            True,
            datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC) - timedelta(hours=24),
        ),
        (
            datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC),
            "price",
            False,
            datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC),
        ),
        (
            datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC),
            "chart",
            False,
            datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC),
        ),
    ],
)
def test_apply_time_delay(
    initial_time: datetime,
    command_type: CommandType,
    apply_delay: bool,
    expected_time: datetime,
) -> None:
    result = apply_time_delay_to_command(
        initial_time, command_type, apply_delay
    )
    assert result == expected_time


@pytest.mark.parametrize(
    "max_cutoff_days",
    [
        30,
        60,
        90,
        180,
        365,
    ],
)
def test_generate_random_date_within_cutoff(max_cutoff_days: int) -> None:
    result = generate_random_date_within_cutoff(max_cutoff_days)

    # Check format (DDMMMYY)
    assert len(result) == 7
    assert result[:2].isdigit()
    assert result[2:5].isalpha()
    assert result[5:].isdigit()

    result_date = datetime.strptime(result, "%d%b%y").replace(tzinfo=UTC)

    assert result_date.date() > datetime.now(UTC).date()

    min_date = datetime.now(UTC) + timedelta(days=7)
    assert result_date.date() >= min_date.date()

    max_date = datetime.now(UTC) + timedelta(days=max_cutoff_days)
    assert result_date.date() <= max_date.date()


class TestLookbackUtils:
    """Test cases for lookback utility functions."""

    def test_parse_lookback_period_valid_periods(self) -> None:
        """Test parsing of valid lookback periods."""
        # Test year-to-date
        current_year = datetime.now(UTC).year
        start_of_year = datetime(current_year, 1, 1, tzinfo=UTC)
        expected_ytd_days = (datetime.now(UTC) - start_of_year).days

        assert parse_lookback_period("ytd") == expected_ytd_days
        assert parse_lookback_period("1m") == 30
        assert parse_lookback_period("2m") == 60
        assert parse_lookback_period("3m") == 90
        assert parse_lookback_period("6m") == 180
        assert parse_lookback_period("1y") == 365
        assert parse_lookback_period("2y") == 730

    def test_parse_lookback_period_invalid_periods(self) -> None:
        """Test parsing of invalid lookback periods."""
        with pytest.raises(ValueError, match="Invalid lookback period"):
            parse_lookback_period("invalid")  # type: ignore

        with pytest.raises(ValueError, match="Invalid lookback period"):
            parse_lookback_period("5y")  # type: ignore

    def test_is_valid_lookback_suffix(self) -> None:
        """Test validation of lookback suffixes."""
        # Valid suffixes
        assert is_valid_lookback_suffix("ytd") is True
        assert is_valid_lookback_suffix("1m") is True
        assert is_valid_lookback_suffix("2m") is True
        assert is_valid_lookback_suffix("3m") is True
        assert is_valid_lookback_suffix("6m") is True
        assert is_valid_lookback_suffix("1y") is True
        assert is_valid_lookback_suffix("2y") is True

        # Invalid suffixes
        assert is_valid_lookback_suffix("5y") is False
        assert is_valid_lookback_suffix("invalid") is False
        assert is_valid_lookback_suffix("") is False
