import logging

import pytest

from telegram_bot.typings import AggregatedNetGreeksResponse, NetGreeksResponse


def test_single_response_includes_implied_vol(
    caplog: pytest.LogCaptureFixture,
) -> None:
    # Use a fixed timestamp for consistency
    fixed_timestamp = 1700000000 * 1e9

    response = NetGreeksResponse(
        spot=50000.0,
        pricing_timestamp=int(fixed_timestamp),
        implied_vol=0.25,
        price=1000.0,
        delta=0.5,
        theta=-0.1,
        gamma=0.05,
        vega=0.2,
    )
    with pytest.raises(ValueError), caplog.at_level(logging.ERROR):
        AggregatedNetGreeksResponse.aggregate_single_currency_position(
            [response], logger=logging.getLogger()
        )

        assert (
            "Only One NetGreeksResponse provided. Can only aggregate multiple responses"
            in caplog.text
        )


def test_multiple_responses_within_2_minutes() -> None:
    # Define two timestamps within 2 minutes (120 seconds)
    min_timestamp = 1700000000 * 1e9
    max_timestamp = min_timestamp + 60 * 1e9  # 60 seconds later

    response1 = NetGreeksResponse(
        spot=50000.0,
        pricing_timestamp=int(min_timestamp),
        implied_vol=0.25,
        price=1000.0,
        delta=0.5,
        theta=-0.15,
        gamma=0.05,
        vega=0.2,
    )
    response2 = NetGreeksResponse(
        spot=49950.0,
        pricing_timestamp=int(max_timestamp),
        implied_vol=0.30,
        price=1500.0,
        delta=0.7,
        theta=-0.25,
        gamma=0.07,
        vega=0.25,
    )
    aggregated = AggregatedNetGreeksResponse.aggregate_single_currency_position(
        [response1, response2], logger=logging.getLogger()
    )

    assert aggregated.spot == 49975.0  # Median spot
    assert aggregated.pricing_timestamp == min_timestamp  # Min timestamp
    assert aggregated.price == 2500.0
    assert aggregated.delta == 1.2
    assert aggregated.theta == -0.4
    assert aggregated.gamma == 0.12
    assert aggregated.vega == 0.45


def test_multiple_responses_exceeding_2_minutes(
    caplog: pytest.LogCaptureFixture,
) -> None:
    # Define two timestamps exceeding 2 minutes (121 seconds)
    min_timestamp = 1700000000 * 1e9
    max_timestamp = min_timestamp + 121 * 1e9  # 121 seconds later

    response1 = NetGreeksResponse(
        spot=50000.0,
        pricing_timestamp=int(min_timestamp),
        implied_vol=0.25,
        price=1000.0,
        delta=0.5,
        theta=-0.1,
        gamma=0.05,
        vega=0.2,
    )
    response2 = NetGreeksResponse(
        spot=49950.0,
        pricing_timestamp=int(max_timestamp),
        implied_vol=0.30,
        price=1500.0,
        delta=0.7,
        theta=-0.2,
        gamma=0.07,
        vega=0.25,
    )

    with caplog.at_level(logging.ERROR), pytest.raises(ValueError):
        AggregatedNetGreeksResponse.aggregate_single_currency_position(
            [response1, response2], logger=logging.getLogger()
        )

    assert (
        "Pricing timestamps differ by 121.0 seconds, which exceeds the 2-minute threshold."
        in caplog.text
    )


def test_multiple_responses_exactly_2_minutes_apart() -> None:
    # Define two timestamps exactly 2 minutes (120 seconds) apart
    min_timestamp = 1700000000 * 1e9
    max_timestamp = min_timestamp + 120 * 1e9  # 120 seconds later

    response1 = NetGreeksResponse(
        spot=50000.0,
        pricing_timestamp=int(min_timestamp),
        implied_vol=0.25,
        price=1000.0,
        delta=0.5,
        theta=-0.1,
        gamma=0.05,
        vega=0.2,
    )
    response2 = NetGreeksResponse(
        spot=49950.0,
        pricing_timestamp=int(max_timestamp),
        implied_vol=0.30,
        price=1500.0,
        delta=0.7,
        theta=-0.2,
        gamma=0.07,
        vega=0.25,
    )
    aggregated = AggregatedNetGreeksResponse.aggregate_single_currency_position(
        [response1, response2], logger=logging.getLogger()
    )

    assert aggregated.spot == 49975.0  # Median spot
    assert aggregated.pricing_timestamp == min_timestamp  # Min timestamp
    assert aggregated.price == 2500.0
    assert aggregated.delta == 1.2
    assert aggregated.theta == -0.3
    assert aggregated.gamma == 0.12
    assert aggregated.vega == 0.45


def test_empty_response_list_raises_error() -> None:
    with pytest.raises(ValueError) as exc_info:
        AggregatedNetGreeksResponse.aggregate_single_currency_position(
            [], logger=logging.getLogger()
        )
    assert (
        str(exc_info.value)
        == "No NetGreeksResponse objects provided for aggregation"
    )


def test_aggregate_with_three_responses_within_2_minutes() -> None:
    # Define three timestamps within 2 minutes
    min_timestamp = 1700000000 * 1e9
    timestamps = [
        min_timestamp,
        min_timestamp + 10 * 1e9,  # 10 seconds later
        min_timestamp + 20 * 1e9,  # 20 seconds later
    ]

    response1 = NetGreeksResponse(
        spot=50000.0,
        pricing_timestamp=int(timestamps[0]),
        implied_vol=0.25,
        price=1000.0,
        delta=0.5,
        theta=-0.1,
        gamma=0.05,
        vega=0.2,
    )
    response2 = NetGreeksResponse(
        spot=49950.0,
        pricing_timestamp=int(timestamps[1]),
        implied_vol=0.30,
        price=1500.0,
        delta=0.7,
        theta=-0.2,
        gamma=0.07,
        vega=0.25,
    )
    response3 = NetGreeksResponse(
        spot=49900.0,
        pricing_timestamp=int(timestamps[2]),
        implied_vol=0.28,
        price=2000.0,
        delta=0.8,
        theta=-0.15,
        gamma=0.06,
        vega=0.22,
    )
    aggregated = AggregatedNetGreeksResponse.aggregate_single_currency_position(
        [response1, response2, response3], logger=logging.getLogger()
    )

    assert aggregated.spot == 49950.0  # Minimum spot
    assert aggregated.pricing_timestamp == min_timestamp  # Min timestamp
    assert aggregated.price == 4500.0
    assert aggregated.delta == 2.0
    assert aggregated.theta == -0.45
    assert aggregated.gamma == 0.18
    assert aggregated.vega == 0.67
