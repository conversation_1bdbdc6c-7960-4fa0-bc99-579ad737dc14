import pytest

from telegram_bot.configs.bot_config import Botfig
from telegram_bot.exceptions import (
    ChartValidationError,
    PermissionDeniedException,
)
from telegram_bot.handlers.validate import (
    ButterflyChartValidator,
    FundingRateValidator,
    PerpPriceValidator,
    SkewChartValidator,
    SmileChartValidator,
    SpotValidator,
    VolatilityValidatorBase,
    VolChartValidator,
)
from telegram_bot.typings import (
    ValidatedFundingRateRequest,
    ValidatedSmileRequest,
    ValidatedSpotRequest,
    ValidatedVolRequest,
)


# ---------------------------------------------------------------------------
# VolatilityValidatorBase Tests
# ---------------------------------------------------------------------------
class TestVolatilityValidatorBase:

    def test_invalid_chart_type(self, botfig: Botfig) -> None:
        args = ["vol", "SOL", "delta1"]
        with pytest.raises(ChartValidationError) as excinfo:
            VolatilityValidatorBase.validate(args, botfig, chart_type="invalid")
        assert "Invalid chart type" in str(excinfo.value)

    def test_invalid_num_args(self, botfig: Botfig) -> None:
        args = ["vol", "SOL"]  # Too few arguments
        with pytest.raises(ChartValidationError) as excinfo:
            VolatilityValidatorBase.validate(args, botfig, chart_type="vol")
        assert "Invalid number of arguments provided for 'vol' chart" in str(
            excinfo.value
        )

    def test_invalid_currency(self, botfig: Botfig) -> None:
        args = ["vol", "XYZ", "delta1"]
        with pytest.raises(ChartValidationError) as excinfo:
            VolatilityValidatorBase.validate(args, botfig, chart_type="vol")
        assert "Unsupported currency" in str(excinfo.value)

    def test_invalid_chart_target(self, botfig: Botfig) -> None:
        args = ["vol", "SOL", "not_a_valid_delta"]
        with pytest.raises(ChartValidationError) as excinfo:
            VolatilityValidatorBase.validate(args, botfig, chart_type="vol")
        assert "Invalid delta supplied" in str(excinfo.value)

    def test_skew_atm_rejected(self, botfig: Botfig) -> None:
        args = ["vol", "SOL", "atm"]
        with pytest.raises(ChartValidationError) as excinfo:
            VolatilityValidatorBase.validate(args, botfig, chart_type="skew")
        assert "Cannot plot 'atm'" in str(excinfo.value)

    def test_skew_negative_delta(self, botfig: Botfig) -> None:
        args = ["vol", "SOL", "-10delta"]
        with pytest.raises(ChartValidationError) as excinfo:
            VolatilityValidatorBase.validate(args, botfig, chart_type="skew")
        assert "negative delta" in str(excinfo.value).lower()

    def test_valid_vol_chart(self, botfig: Botfig) -> None:
        args = ["vol", "SOL", "atm"]
        result = VolatilityValidatorBase.validate(
            args, botfig, chart_type="vol"
        )
        assert isinstance(result, ValidatedVolRequest)
        assert result.chart_type == "vol"
        assert result.currency == "SOL"
        assert result.chart_target == "atm"


# ---------------------------------------------------------------------------
# VolChartValidator Tests
# ---------------------------------------------------------------------------
class TestVolChartValidator:

    def test_valid_vol_chart(self, botfig: Botfig) -> None:
        args = ["vol", "SOL", "50delta"]
        result = VolChartValidator.validate(args, botfig)
        assert isinstance(result, ValidatedVolRequest)
        assert result.chart_type == "vol"

    def test_invalid_vol_chart_num_args(self, botfig: Botfig) -> None:
        args = ["vol", "SOL"]
        with pytest.raises(ChartValidationError):
            VolChartValidator.validate(args, botfig)

    def test_vol_chart_with_lookback_whitelisted_user(
        self, botfig: Botfig
    ) -> None:
        """Test vol chart with lookback period for whitelisted user (apply_delay=False)."""
        args = ["vol", "SOL", "50delta", "3m"]

        result = VolChartValidator.validate(args, botfig, apply_delay=False)
        assert isinstance(result, ValidatedVolRequest)
        assert result.chart_type == "vol"
        assert result.lookback_period == "3m"

    def test_vol_chart_with_lookback_non_whitelisted_user(
        self, botfig: Botfig
    ) -> None:
        """Test vol chart with lookback period for non-whitelisted user (apply_delay=True)."""
        args = ["vol", "SOL", "50delta", "3m"]

        with pytest.raises(
            PermissionDeniedException,
            match="Historical lookback periods are only available to whitelisted users",
        ):
            VolChartValidator.validate(args, botfig, apply_delay=True)

    def test_vol_chart_with_invalid_lookback(self, botfig: Botfig) -> None:
        """Test vol chart with invalid lookback period."""
        args = ["vol", "SOL", "50delta", "invalid"]

        with pytest.raises(
            ChartValidationError, match="Invalid lookback period"
        ):
            VolChartValidator.validate(args, botfig, apply_delay=False)

    def test_vol_chart_all_valid_lookback_periods(self, botfig: Botfig) -> None:
        """Test vol chart with all valid lookback periods."""
        valid_periods = ["ytd", "2m", "3m", "6m", "1y", "2y"]

        for period in valid_periods:
            args = ["vol", "SOL", "50delta", period]
            result = VolChartValidator.validate(args, botfig, apply_delay=False)
            assert isinstance(result, ValidatedVolRequest)
            assert result.lookback_period == period

    def test_vol_chart_without_lookback_period(self, botfig: Botfig) -> None:
        """Test vol chart without lookback period (should work for all users)."""
        args = ["vol", "SOL", "50delta"]

        # Should work for both whitelisted and non-whitelisted users
        result_whitelisted = VolChartValidator.validate(
            args, botfig, apply_delay=False
        )
        result_non_whitelisted = VolChartValidator.validate(
            args, botfig, apply_delay=True
        )

        assert isinstance(result_whitelisted, ValidatedVolRequest)
        assert isinstance(result_non_whitelisted, ValidatedVolRequest)
        assert result_whitelisted.lookback_period is None
        assert result_non_whitelisted.lookback_period is None


# ---------------------------------------------------------------------------
# SkewChartValidator Tests
# ---------------------------------------------------------------------------
class TestSkewChartValidator:

    def test_valid_skew_chart(self, botfig: Botfig) -> None:
        args = ["skew", "BTC", "1delta"]
        result = SkewChartValidator.validate(args, botfig)
        assert isinstance(result, ValidatedVolRequest)
        assert result.chart_type == "skew"

    def test_skew_chart_with_lookback_whitelisted_user(
        self, botfig: Botfig
    ) -> None:
        """Test skew chart with lookback period for whitelisted user."""
        args = ["skew", "BTC", "25delta", "6m"]

        result = SkewChartValidator.validate(args, botfig, apply_delay=False)
        assert isinstance(result, ValidatedVolRequest)
        assert result.chart_type == "skew"
        assert result.lookback_period == "6m"

    def test_skew_chart_with_lookback_non_whitelisted_user(
        self, botfig: Botfig
    ) -> None:
        """Test skew chart with lookback period for non-whitelisted user."""
        args = ["skew", "BTC", "25delta", "6m"]

        with pytest.raises(
            PermissionDeniedException,
            match="Historical lookback periods are only available to whitelisted users",
        ):
            SkewChartValidator.validate(args, botfig, apply_delay=True)

    def test_valid_skew_invalid_target(self, botfig: Botfig) -> None:
        args = ["skew", "BTC", "44delta"]
        with pytest.raises(ChartValidationError) as excinfo:
            SkewChartValidator.validate(args, botfig)
        assert "Invalid delta supplied for 'skew' chart" in str(excinfo.value)

    def test_skew_chart_atm(self, botfig: Botfig) -> None:
        args = ["skew", "BTC", "atm"]
        with pytest.raises(ChartValidationError) as excinfo:
            SkewChartValidator.validate(args, botfig)
        assert "Cannot plot 'atm'" in str(excinfo.value)

    def test_skew_chart_50delta(self, botfig: Botfig) -> None:
        args = ["skew", "BTC", "50delta"]
        with pytest.raises(ChartValidationError) as excinfo:
            SkewChartValidator.validate(args, botfig)
        assert "Cannot plot 'atm' or '50delta'" in str(excinfo.value)

    def test_skew_chart_negative_delta(self, botfig: Botfig) -> None:
        args = ["skew", "BTC", "-10delta"]
        with pytest.raises(ChartValidationError) as excinfo:
            SkewChartValidator.validate(args, botfig)
        assert "negative delta" in str(excinfo.value).lower()


# ---------------------------------------------------------------------------
# ButterflyChartValidator Tests
# ---------------------------------------------------------------------------
class TestButterflyChartValidator:

    def test_valid_butterfly_chart(self, botfig: Botfig) -> None:
        args = ["butterfly", "BTC", "25delta"]
        result = ButterflyChartValidator.validate(args, botfig)
        assert isinstance(result, ValidatedVolRequest)
        assert result.chart_type == "butterfly"

    def test_butterfly_chart_with_lookback_whitelisted_user(
        self, botfig: Botfig
    ) -> None:
        """Test butterfly chart with lookback period for whitelisted user."""
        args = ["butterfly", "BTC", "25delta", "1y"]

        result = ButterflyChartValidator.validate(
            args, botfig, apply_delay=False
        )
        assert isinstance(result, ValidatedVolRequest)
        assert result.chart_type == "butterfly"
        assert result.lookback_period == "1y"

    def test_butterfly_chart_with_lookback_non_whitelisted_user(
        self, botfig: Botfig
    ) -> None:
        """Test butterfly chart with lookback period for non-whitelisted user."""
        args = ["butterfly", "BTC", "25delta", "1y"]

        with pytest.raises(
            PermissionDeniedException,
            match="Historical lookback periods are only available to whitelisted users",
        ):
            ButterflyChartValidator.validate(args, botfig, apply_delay=True)

    def test_butterfly_chart_atm(self, botfig: Botfig) -> None:
        args = ["butterfly", "BTC", "atm"]
        with pytest.raises(ChartValidationError) as excinfo:
            ButterflyChartValidator.validate(args, botfig)
        assert "Cannot plot 'atm' or '50delta'" in str(excinfo.value)

    def test_butterfly_chart_negative_delta(self, botfig: Botfig) -> None:
        args = ["butterfly", "BTC", "-10delta"]
        with pytest.raises(ChartValidationError) as excinfo:
            ButterflyChartValidator.validate(args, botfig)
        assert "negative delta" in str(excinfo.value).lower()


# ---------------------------------------------------------------------------
# SmileChartValidator Tests
# ---------------------------------------------------------------------------
class TestSmileChartValidator:
    def test_invalid_num_args(self, botfig: Botfig) -> None:
        args = ["smile", "BTC"]  # Only 2 args provided
        with pytest.raises(ChartValidationError) as excinfo:
            SmileChartValidator.validate(args, botfig)
        assert "Invalid number of arguments provided for 'smile' chart" in str(
            excinfo.value
        )

    def test_invalid_currency(self, botfig: Botfig) -> None:
        args = ["smile", "TON", "surface"]
        with pytest.raises(ChartValidationError) as excinfo:
            SmileChartValidator.validate(args, botfig)
        assert "Unsupported currency" in str(excinfo.value)

    def test_invalid_constant_tenor(self, botfig: Botfig) -> None:
        args = ["smile", "BTC", "500d"]
        with pytest.raises(ChartValidationError) as excinfo:
            SmileChartValidator.validate(args, botfig)
        assert "Unsupported chart target" in str(excinfo.value)

    def test_valid_constant_tenor(self, botfig: Botfig) -> None:
        args = ["smile", "BTC", "7d"]
        result = SmileChartValidator.validate(args, botfig)
        assert isinstance(result, ValidatedSmileRequest)
        assert result.chart_target == "7d"

    def test_listed_expiry_expired(self, botfig: Botfig) -> None:
        args = ["smile", "BTC", "22MAY00"]
        with pytest.raises(ChartValidationError) as excinfo:
            SmileChartValidator.validate(args, botfig)
        assert "Expiry is in the past" in str(excinfo.value)

    def test_unrecognised_chart_target(self, botfig: Botfig) -> None:
        args = ["smile", "SOL", "invalid"]
        with pytest.raises(ChartValidationError) as excinfo:
            SmileChartValidator.validate(args, botfig)
        assert "Unrecognised tenor 'invalid'" in str(excinfo.value)

    def test_valid_surface(self, botfig: Botfig) -> None:
        args = ["smile", "ETH", "surface"]
        result = SmileChartValidator.validate(args, botfig)
        assert isinstance(result, ValidatedSmileRequest)
        assert result.chart_type == "smile"
        assert result.currency == "ETH"
        assert result.chart_target.lower() == "surface"


# ---------------------------------------------------------------------------
# SpotChartValidator Tests
# ---------------------------------------------------------------------------
class TestSpotChartValidator:
    def test_valid(self, botfig: Botfig) -> None:
        curr = "eTh"
        args = ["spot", "eth", "kraken"]
        result = SpotValidator.validate(args, botfig=botfig)
        assert isinstance(result, ValidatedSpotRequest)
        assert result.chart_type == "spot"
        assert result.exchange == "kraken"
        assert result.currency == curr.upper()

    def test_too_few_args(self, botfig: Botfig) -> None:
        # Test with incorrect number of arguments (only one argument provided)
        args = ["spot"]
        with pytest.raises(ChartValidationError) as excinfo:
            SpotValidator.validate(args, botfig=botfig)
        assert "Invalid number of arguments provided for 'spot' chart." in str(
            excinfo.value
        )

    def test_too_many_args(self, botfig: Botfig) -> None:
        # Test with incorrect number of arguments
        args = ["spot", "BTC", "kraken", "extra"]
        with pytest.raises(ChartValidationError) as excinfo:
            SpotValidator.validate(args, botfig=botfig)
        assert "Invalid number of arguments provided for 'spot' chart." in str(
            excinfo.value
        )

    def test_invalid_exchange(self, botfig: Botfig) -> None:
        # Use an exchange that is not present in the funding_rate_config.
        with pytest.raises(ChartValidationError) as excinfo:
            SpotValidator.validate(["spot", "BTC", "nonexistent"], botfig)
        assert "Unsupported exchange for 'spot' chart" in str(excinfo.value)

    def test_invalid_currency(self, botfig: Botfig) -> None:
        # Provide a valid exchange but an unsupported currency.
        with pytest.raises(ChartValidationError) as excinfo:
            SpotValidator.validate(["spot", "CCC", "kraken"], botfig)
        assert "Unsupported currency 'CCC' for 'kraken'" in str(excinfo.value)


# ---------------------------------------------------------------------------
# FundingRateChartValidator Tests
# ---------------------------------------------------------------------------
class TestFundingRateValidator:
    def test_invalid_num_args(self, botfig: Botfig) -> None:
        # Test with fewer than three arguments.
        with pytest.raises(ChartValidationError) as excinfo:
            FundingRateValidator.validate(["fr", "BTC"], botfig)
        assert "Invalid number of arguments provided for 'fr' chart." in str(
            excinfo.value
        )

        # Test with more than three arguments.
        with pytest.raises(ChartValidationError) as excinfo:
            FundingRateValidator.validate(
                ["fr", "BTC", "deribit", "extra_arg"], botfig
            )
        assert "Invalid number of arguments provided for 'fr' chart." in str(
            excinfo.value
        )

    def test_invalid_exchange(self, botfig: Botfig) -> None:
        # Use an exchange that is not present in the funding_rate_config.
        with pytest.raises(ChartValidationError) as excinfo:
            FundingRateValidator.validate(["fr", "BTC", "nonexistent"], botfig)
        assert "Unsupported exchange for 'fr' chart" in str(excinfo.value)

    def test_invalid_currency(self, botfig: Botfig) -> None:
        # Provide a valid exchange but an unsupported currency.
        with pytest.raises(ChartValidationError) as excinfo:
            FundingRateValidator.validate(["fr", "CCC", "bybit"], botfig)
        assert "Unsupported currency 'CCC' for 'bybit'" in str(excinfo.value)

    def test_valid_funding_rate(self, botfig: Botfig) -> None:
        # Provide valid inputs. Note that the currency is case-insensitive.
        result = FundingRateValidator.validate(["fr", "BnB", "binance"], botfig)
        assert isinstance(result, ValidatedFundingRateRequest)
        assert result.chart_type == "fr"
        assert result.currency == "BNB"
        assert result.exchange == "binance"


# ---------------------------------------------------------------------------
# PerpetualPriceChartValidator Tests
# ---------------------------------------------------------------------------
class TestPerpPriceValidator:

    def test_perp_price_validator_invalid_args_count(
        self, botfig: Botfig
    ) -> None:
        # Fewer than three arguments.
        with pytest.raises(ChartValidationError):
            PerpPriceValidator.validate(["perp", "eth"], botfig)
        # More than three arguments.
        with pytest.raises(ChartValidationError):
            PerpPriceValidator.validate(
                ["perp", "sol", "binance", "extra"], botfig
            )

    def test_perp_price_validator_unsupported_exchange(
        self, botfig: Botfig
    ) -> None:
        # The provided exchange 'kraken' is not in the funding_rate_config.
        args = ["perp", "btc", "kraken"]
        with pytest.raises(ChartValidationError):
            PerpPriceValidator.validate(args, botfig)

    def test_perp_price_validator_supported_exchange(
        self, botfig: Botfig
    ) -> None:
        args = ["perp", "btc", "binance"]
        result = PerpPriceValidator.validate(args, botfig)
        assert result.currency == "BTC"
        assert result.exchange == "binance"

    def test_perp_price_validator_unsupported_currency(
        self, botfig: Botfig
    ) -> None:
        # For 'binance', only USD and BTC are supported.
        args = ["perp", "eur", "binance"]  # 'EUR' is unsupported.
        with pytest.raises(ChartValidationError):
            PerpPriceValidator.validate(args, botfig)

    def test_perp_price_validator_capitalization(self, botfig: Botfig) -> None:
        # Ensure that even with various capitalizations, the validator returns normalized values.
        args = ["PERP", "etH", "bInAnCe"]
        result = PerpPriceValidator.validate(args, botfig)
        assert result.currency == "ETH"
        assert result.exchange == "binance"
