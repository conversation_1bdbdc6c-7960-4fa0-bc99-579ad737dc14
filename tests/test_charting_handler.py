import io
from typing import Any
from unittest.mock import patch

import pytest

from telegram_bot.exceptions import ImageNotFoundError
from telegram_bot.handlers.charting import get_decoded_images_from_response


@pytest.mark.asyncio
async def test_get_decoded_images_from_response_backwards_compatibility() -> (
    None
):
    """Test function works with old string format (EncodedCharts)"""
    # Mock response body with old format (strings)
    response_body = {
        "output": {
            "results": {
                "v2timeseries": {
                    "chart1": "base64_encoded_string_data",
                    "chart2": "another_base64_string",
                }
            }
        }
    }

    mock_image = io.BytesIO(b"fake_image_data")

    with (
        patch(
            "telegram_bot.handlers.charting.decode_image"
        ) as mock_decode_image,
    ):
        mock_decode_image.return_value = mock_image

        result = await get_decoded_images_from_response(response_body, "vol")

        # Verify decode_image was called with string data
        assert mock_decode_image.call_count == 2
        mock_decode_image.assert_any_call("base64_encoded_string_data")
        mock_decode_image.assert_any_call("another_base64_string")

        # Verify structure
        assert "v2timeseries" in result
        assert "chart1" in result["v2timeseries"]
        assert "chart2" in result["v2timeseries"]


@pytest.mark.asyncio
async def test_get_decoded_images_from_response_new_format() -> None:
    """Test function works with new LambdaPayload format"""
    # Mock response body with new format (dict with "image" key)
    response_body = {
        "output": {
            "results": {
                "v2smiles": {
                    "smile_chart": {
                        "image": "new_base64_encoded_data",
                        "data": {
                            "some_plot_target": [
                                {"value": 1, "timestamp": 1234567890}
                            ]
                        },
                    }
                }
            }
        }
    }

    mock_image = io.BytesIO(b"fake_image_data")

    with (
        patch(
            "telegram_bot.handlers.charting.decode_image"
        ) as mock_decode_image,
    ):
        mock_decode_image.return_value = mock_image

        result = await get_decoded_images_from_response(response_body, "smile")

        # Verify decode_image was called with image data from dict
        mock_decode_image.assert_called_once_with("new_base64_encoded_data")

        # Verify structure
        assert "v2smiles" in result
        assert "smile_chart" in result["v2smiles"]


@pytest.mark.asyncio
async def test_get_decoded_images_from_response_no_images() -> None:
    """Test function raises error when no images found"""
    response_body: dict[str, Any] = {}

    with pytest.raises(
        ImageNotFoundError, match="No images found in the response body"
    ):
        await get_decoded_images_from_response(response_body, "vol")


@pytest.mark.asyncio
async def test_get_decoded_images_from_response_unexpected_payload_type() -> (
    None
):
    """Test function raises error for unexpected payload type"""
    response_body = {
        "output": {
            "results": {
                "v2timeseries": {"chart1.png": 12345}  # Neither string nor dict
            }
        }
    }

    with pytest.raises(ValueError, match="Unexpected payload type"):
        await get_decoded_images_from_response(response_body, "vol")


@pytest.mark.asyncio
async def test_get_decoded_images_from_response_no_results_for_chart_type() -> (
    None
):
    """When results exist but not for the requested chart type, raise ImageNotFoundError"""
    response_body = {
        "output": {
            "results": {
                # Only smiles present; request a vol chart
                "v2smiles": {"smile_chart": {"image": "abc"}}
            }
        }
    }
    with pytest.raises(
        ImageNotFoundError,
        match="No images found for chart type 'v2timeseries'",
    ):
        await get_decoded_images_from_response(response_body, "vol")


@pytest.mark.asyncio
async def test_get_decoded_images_from_response_no_image_only_data() -> None:
    """When lambda returns only data (no image), raise ImageNotFoundError"""
    response_body = {
        "output": {"results": {"v2smiles": {"smile_chart": {"data": "abc"}}}}
    }
    with pytest.raises(
        ImageNotFoundError,
        match="No images found for chart type 'v2smiles'",
    ):
        await get_decoded_images_from_response(response_body, "smile")
