from telegram_bot.configs.bot_config import Botfig
from telegram_bot.handlers.validate import SignalValidator
from telegram_bot.typings import ValidatedSignalChartRequest


class TestSignalValidator:
    """Test cases for SignalValidator."""

    def test_valid_signal_request_no_args(self, botfig: Botfig) -> None:
        """Test valid signal request with no arguments."""
        args: list[str] = []
        result = SignalValidator.validate(args, botfig)

        assert isinstance(result, ValidatedSignalChartRequest)
        assert result.chart_type == "wing spread"

    def test_valid_signal_request_with_args(self, botfig: Botfig) -> None:
        """Test valid signal request with arguments (should be ignored)."""
        args = ["BTC"]
        result = SignalValidator.validate(args, botfig)

        assert isinstance(result, ValidatedSignalChartRequest)
        assert result.chart_type == "wing spread"

    def test_valid_signal_request_with_multiple_args(
        self, botfig: Botfig
    ) -> None:
        """Test valid signal request with multiple arguments (should be ignored)."""
        args = ["BTC", "extra", "args"]
        result = SignalValidator.validate(args, botfig)

        assert isinstance(result, ValidatedSignalChartRequest)
        assert result.chart_type == "wing spread"
