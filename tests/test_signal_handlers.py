from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from telegram_bot.configs.bot_config import Botfig
from telegram_bot.handlers.signal import (
    _build_and_send_signal_charts,
    _is_chat_id_permissioned_for_signal,
    _process_signal_arguments,
    handle_signal_request,
)
from telegram_bot.typings import ValidatedSignalChartRequest


class TestSignalHandlers:
    """Test cases for signal handler functions."""

    def test_is_chat_id_permissioned_for_signal(self) -> None:
        """Test _is_chat_id_permissioned_for_signal function."""
        # Test with None chat_id
        assert _is_chat_id_permissioned_for_signal(None) is False

        # Test with chat_id not in PERMISSIONED_SIGNAL_CHAT_IDS
        assert _is_chat_id_permissioned_for_signal(999999) is False

        # Test with chat_id in PERMISSIONED_SIGNAL_CHAT_IDS
        # Create a temporary mock for testing
        with patch(
            "telegram_bot.handlers.signal.PERMISSIONED_SIGNAL_CHAT_IDS",
            [123456],
        ):
            assert _is_chat_id_permissioned_for_signal(123456) is True

    def test_process_signal_arguments(self, botfig: Botfig) -> None:
        """Test _process_signal_arguments function."""
        # Create mock context
        context = AsyncMock()
        context.args = []

        # Test with empty args
        result = _process_signal_arguments(context, botfig)
        assert isinstance(result, ValidatedSignalChartRequest)
        assert result.chart_type == "wing spread"

        # Test with some args (should be ignored)
        context.args = ["BTC"]
        result = _process_signal_arguments(context, botfig)
        assert isinstance(result, ValidatedSignalChartRequest)
        assert result.chart_type == "wing spread"

    @pytest.mark.asyncio
    async def test_handle_signal_request_no_effective_chat(
        self, botfig: Botfig
    ) -> None:
        """Test handle_signal_request with no effective chat."""
        # Create mock objects
        update = MagicMock()
        update.effective_chat = None
        update.effective_user = MagicMock()
        context = AsyncMock()
        logger = MagicMock()

        # Call the function
        await handle_signal_request(update, context, botfig, logger)

        # Assert logger.error was called
        logger.error.assert_called_once_with("Effective User or Chat not found")

    @pytest.mark.asyncio
    async def test_handle_signal_request_no_effective_user(
        self, botfig: Botfig
    ) -> None:
        """Test handle_signal_request with no effective user."""
        # Create mock objects
        update = MagicMock()
        update.effective_chat = MagicMock()
        update.effective_user = None
        context = AsyncMock()
        logger = MagicMock()

        # Call the function
        await handle_signal_request(update, context, botfig, logger)

        # Assert logger.error was called
        logger.error.assert_called_once_with("Effective User or Chat not found")

    @pytest.mark.asyncio
    async def test_handle_signal_request_unpermissioned_chat(
        self, botfig: Botfig
    ) -> None:
        """Test handle_signal_request with unpermissioned chat ID."""
        # Create mock objects
        update = MagicMock()
        update.effective_chat = MagicMock()
        update.effective_chat.id = 999999
        update.effective_user = MagicMock()
        context = AsyncMock()
        logger = MagicMock()

        # Mock send_failed_request_message
        send_message_mock = context.bot.send_message = AsyncMock()
        with patch(
            "telegram_bot.handlers.signal.PERMISSIONED_SIGNAL_CHAT_IDS",
            [99999991],
        ):
            # Call the function
            await handle_signal_request(update, context, botfig, logger)

            # Assert send_failed_request_message was called with permission denied message
            send_message_mock.assert_called_once_with(
                chat_id=update.effective_chat.id,
                text="You are not authorized to use the /signal command in this chat. Please /contact a member of the BlockScholes team for access.",
            )

    @pytest.mark.asyncio
    async def test_handle_signal_request_permissioned_chat(
        self, botfig: Botfig
    ) -> None:
        """Test handle_signal_request with permissioned chat ID."""
        # Create mock objects
        update = MagicMock()
        update.effective_chat = MagicMock()
        update.effective_chat.id = 123456
        update.effective_user = MagicMock()
        context = AsyncMock()
        logger = MagicMock()

        expected_signal_request = ValidatedSignalChartRequest(
            chart_type="wing spread"
        )

        # Mock the permissioned chat IDs to include our test chat ID
        with patch(
            "telegram_bot.handlers.signal.PERMISSIONED_SIGNAL_CHAT_IDS",
            [123456],
        ):
            # Mock build_and_send_signal_charts
            build_mock = AsyncMock()
            with patch(
                "telegram_bot.handlers.signal._build_and_send_signal_charts",
                build_mock,
            ):
                # Call the function
                await handle_signal_request(update, context, botfig, logger)

                # Assert build_and_send_signal_charts was called, indicating permission check passed
                build_mock.assert_called_once_with(
                    signal_request=expected_signal_request,
                    context=context,
                    update=update,
                    botfig=botfig,
                    logger=logger,
                )

    @pytest.mark.asyncio
    async def test_handle_signal_request_process_error(
        self, botfig: Botfig
    ) -> None:
        """Test handle_signal_request with error in _process_signal_arguments."""
        # Create mock objects
        update = MagicMock()
        update.effective_chat = MagicMock()
        update.effective_user = MagicMock()
        context = AsyncMock()
        logger = MagicMock()

        # Mock _process_signal_arguments to raise an exception
        with patch(
            "telegram_bot.handlers.signal._is_chat_id_permissioned_for_signal",
            return_value=True,
        ):
            # Mock _process_signal_arguments to raise an exception
            with patch(
                "telegram_bot.handlers.signal._process_signal_arguments",
                side_effect=Exception("Test error"),
            ):
                # Call the function
                await handle_signal_request(update, context, botfig, logger)

                # Assert logger.exception was called
                logger.exception.assert_called_once_with(
                    "Unexpected error occurred during argument processing"
                )

    @pytest.mark.asyncio
    async def test_handle_signal_request_success(self, botfig: Botfig) -> None:
        """Test handle_signal_request with successful processing."""

        update = MagicMock()
        update.effective_chat = MagicMock()
        update.effective_user = MagicMock()
        context = AsyncMock()
        logger = MagicMock()

        expected_signal_request = ValidatedSignalChartRequest(
            chart_type="wing spread"
        )

        # Mock build_and_send_signal_charts
        build_mock = AsyncMock()
        # Mock _process_signal_arguments to raise an exception
        with patch(
            "telegram_bot.handlers.signal._is_chat_id_permissioned_for_signal",
            return_value=True,
        ):
            with patch(
                "telegram_bot.handlers.signal._build_and_send_signal_charts",
                build_mock,
            ):
                # Call the function
                await handle_signal_request(update, context, botfig, logger)

                # Assert build_and_send_signal_charts was called with the right arguments
                build_mock.assert_called_once_with(
                    signal_request=expected_signal_request,
                    context=context,
                    update=update,
                    botfig=botfig,
                    logger=logger,
                )

    @pytest.mark.asyncio
    async def test_build_and_send_signal_charts_handle_chart_type_error(
        self, botfig: Botfig
    ) -> None:
        """Test build_and_send_signal_charts with error in handle_chart_type."""
        # Create mock objects
        signal_request = ValidatedSignalChartRequest(chart_type="wing spread")
        update = MagicMock()
        update.effective_chat = MagicMock()
        context = AsyncMock()
        logger = MagicMock()

        # Mock handle_chart_type to raise an exception
        with patch(
            "telegram_bot.handlers.signal.handle_chart_type",
            side_effect=Exception("Test error"),
        ):
            # Call the function
            await _build_and_send_signal_charts(
                signal_request, context, update, botfig, logger
            )

            # Assert logger.exception was called
            logger.exception.assert_called_once_with(
                "Error building signal plot objects"
            )

    @pytest.mark.asyncio
    async def test_build_and_send_signal_charts_construct_rvd_chart_request_error(
        self, botfig: Botfig
    ) -> None:
        """Test build_and_send_signal_charts with error in construct_rvd_chart_request."""
        # Create mock objects
        signal_request = ValidatedSignalChartRequest(chart_type="wing spread")
        update = MagicMock()
        update.effective_chat = MagicMock()
        context = AsyncMock()
        logger = MagicMock()

        # Mock construct_rvd_chart_request to raise an exception
        with patch(
            "telegram_bot.handlers.chart_builder.construct_rvd_chart_request",
            side_effect=Exception("Test error"),
        ):
            # Call the function
            await _build_and_send_signal_charts(
                signal_request, context, update, botfig, logger
            )

            # Assert logger.exception was called
            logger.exception.assert_called_once_with(
                "Error constructing chart request"
            )

    @pytest.mark.asyncio
    async def test_build_and_send_signal_charts_call_lambda_no_response(
        self, botfig: Botfig
    ) -> None:
        """Test build_and_send_signal_charts with no response from call_lambda."""
        # Create mock objects
        signal_request = ValidatedSignalChartRequest(chart_type="wing spread")
        update = MagicMock()
        update.effective_chat = MagicMock()
        context = AsyncMock()
        logger = MagicMock()

        # Mock call_lambda to return None
        with patch(
            "telegram_bot.handlers.signal.call_lambda",
            return_value=None,
        ):
            # Mock send_failed_request_message
            send_failed_mock = AsyncMock()
            with patch(
                "telegram_bot.handlers.signal.send_failed_request_message",
                send_failed_mock,
            ):
                # Call the function
                await _build_and_send_signal_charts(
                    signal_request, context, update, botfig, logger
                )

                # Assert send_failed_request_message was called
                send_failed_mock.assert_called_once_with(
                    context,
                    update,
                    "No charts returned",
                    command_type="signal",
                )

    @pytest.mark.asyncio
    async def test_build_and_send_signal_charts_get_decoded_images_error(
        self, botfig: Botfig
    ) -> None:
        """Test build_and_send_signal_charts with error in get_decoded_images_from_response."""
        # Create mock objects
        signal_request = ValidatedSignalChartRequest(chart_type="wing spread")
        update = MagicMock()
        update.effective_chat = MagicMock()
        context = AsyncMock()
        logger = MagicMock()
        response_body = {"some": "response"}

        with patch(
            "telegram_bot.handlers.signal.call_lambda",
            return_value=response_body,
        ):
            # Mock get_decoded_images_from_response to raise an exception
            with patch(
                "telegram_bot.handlers.signal.get_decoded_images_and_data_from_response",
                side_effect=Exception("Test error"),
            ):
                # Mock send_failed_request_message
                send_failed_mock = AsyncMock()
                with patch(
                    "telegram_bot.handlers.signal.send_failed_request_message",
                    send_failed_mock,
                ):
                    # Call the function
                    await _build_and_send_signal_charts(
                        signal_request, context, update, botfig, logger
                    )

                    # Assert logger.exception was called
                    logger.exception.assert_called_once_with(
                        "Error parsing images"
                    )
                    # Assert send_failed_request_message was called
                    send_failed_mock.assert_called_once()

    @pytest.mark.asyncio
    async def test_build_and_send_signal_charts_success(
        self, botfig: Botfig
    ) -> None:
        """Test build_and_send_signal_charts with successful processing."""
        # Create mock objects
        signal_request = ValidatedSignalChartRequest(chart_type="wing spread")
        update = MagicMock()
        update.effective_chat = MagicMock()
        context = AsyncMock()
        context.bot = AsyncMock()
        context.bot.send_photo = AsyncMock()
        logger = MagicMock()
        response_body = {"some": "response"}

        # Mock decoded images and data
        decoded_images_and_data = {
            "category1": {
                "chart1": {
                    "image": b"image_data1",
                    "data": {
                        "BTC_-10delta_180d": [{"value": 1.23, "timestamp": 1}]
                    },
                },
                "chart2": {
                    "image": b"image_data2",
                    "data": {
                        "BTC_-10delta_180d": [{"value": 2.34, "timestamp": 2}]
                    },
                },
            }
        }

        # Mock call_lambda to return response_body
        with patch(
            "telegram_bot.handlers.signal.call_lambda",
            return_value=response_body,
        ):
            # Mock get_decoded_images_and_data_from_response to return combined payload
            with patch(
                "telegram_bot.handlers.signal.get_decoded_images_and_data_from_response",
                return_value=decoded_images_and_data,
            ):
                # Call the function
                await _build_and_send_signal_charts(
                    signal_request, context, update, botfig, logger
                )

                # Assert context.bot.send_photo was called four times (image + table per chart)
                assert context.bot.send_photo.call_count == 4

                # Assert logger.info was called
                logger.info.assert_called_once_with(
                    "Successfully sent 2 signal charts"
                )
