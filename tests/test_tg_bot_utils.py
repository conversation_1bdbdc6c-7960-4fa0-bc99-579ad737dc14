# python
import pytest

from telegram_bot.constants import (
    DEFAULT_WING_SPREAD_TARGETS,
)
from telegram_bot.tg_bot_utils import (
    get_effective_wing_spread_targets,
    get_wing_spread_threshold,
)


def test_get_effective_wing_spread_targets_returns_default() -> None:
    targets = get_effective_wing_spread_targets()
    assert targets == DEFAULT_WING_SPREAD_TARGETS
    assert len(targets) > 0
    # Keys should be triplets and values floats
    for key, val in targets.items():
        assert isinstance(key, tuple)
        assert len(key) == 3
        assert isinstance(val, float)


def test_get_wing_spread_threshold_found() -> None:
    """
    Verify threshold lookup for an existing triplet returns the expected float.
    """
    # Known entry from the default targets
    found = get_wing_spread_threshold("ETH", "5delta", "180d")
    assert isinstance(found, float)
    assert found == pytest.approx(
        DEFAULT_WING_SPREAD_TARGETS[("ETH", "5delta", "180d")]
    )


def test_get_wing_spread_threshold_not_found() -> None:
    """
    A missing triplet should return None.
    """
    missing = get_wing_spread_threshold("ETH", "5delta", "30d")
    assert missing is None
