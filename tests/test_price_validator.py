from datetime import UTC, datetime, timedelta

import pytest

from telegram_bot.configs.bot_config import Botfig
from telegram_bot.constants import (
    CURRENCY_TO_MAX_PRICING_CUTOFF,
    DEFAULT_MAX_PRICING_CUTOFF,
)
from telegram_bot.exceptions import ChartValidationError, PriceValidationError
from telegram_bot.handlers.validate import OptionPriceValidator
from telegram_bot.typings import AllowedCurrencies, ValidatedPriceRequest


def generate_expiry_date(
    currency: AllowedCurrencies, days_from_now: int = 30
) -> str:
    """
    Generate an expiry date in the format 'DDMMMYY' for a specific currency.

    Args:
        currency: The currency to generate an expiry date for
        days_from_now: Number of days from now for the expiry date

    Returns:
        A date string in the format 'DDMMMYY' (e.g., '22MAY40')
    """
    # Get the max tenor cutoff for the currency

    max_cutoff = CURRENCY_TO_MAX_PRICING_CUTOFF.get(
        currency, DEFAULT_MAX_PRICING_CUTOFF
    )

    # Ensure days_from_now doesn't exceed max_cutoff
    days = min(days_from_now, max_cutoff - 1)

    # Generate the date
    future_date = datetime.now(UTC) + timedelta(days=days)
    return future_date.strftime("%d%b%y").upper()


# ---------------------------------------------------------------------------
# OptionPriceValidator Tests
# ---------------------------------------------------------------------------
class TestOptionPriceValidator:

    def test_invalid_op_type(self, botfig: Botfig) -> None:
        args = ["XYZ", "SOL", "22MAY40", "100"]
        with pytest.raises(PriceValidationError) as excinfo:
            OptionPriceValidator.validate(args, botfig)
        assert "Unsupported option type" in str(excinfo.value)

    def test_invalid_num_args_single_call(self, botfig: Botfig) -> None:
        # For op_type "C" (or similar), exactly 4 args are required.
        for op_type in ["C", "DC", "P", "DP"]:
            args = [op_type, "SOL", "22MAY30"]
            with pytest.raises(PriceValidationError) as excinfo:
                OptionPriceValidator.validate(args, botfig)
            assert (
                "Invalid number of arguments provided for single call option"
                in str(excinfo.value)
            )

    def test_invalid_num_args_butterfly(self, botfig: Botfig) -> None:
        # For op_type "BF", exactly 6 args are required.
        expiry = generate_expiry_date("SOL")
        args = ["BF", "SOL", expiry, "strike1", "strike2"]  # Only 5 args
        with pytest.raises(PriceValidationError) as excinfo:
            OptionPriceValidator.validate(args, botfig)
        assert "Invalid number of arguments provided for butterfly" in str(
            excinfo.value
        )

    def test_invalid_currency(self, botfig: Botfig) -> None:
        expiry = generate_expiry_date("ABC")
        args = ["C", "ABC", expiry, "100"]
        with pytest.raises(PriceValidationError) as excinfo:
            OptionPriceValidator.validate(args, botfig)
        assert "Unsupported currency" in str(excinfo.value)

    def test_expired_option(self, botfig: Botfig) -> None:
        # 69 seems to be the cutoff before the date is considered to be 1969. tested in 2025
        args = ["C", "BTC", "22MAY69", "100"]
        with pytest.raises(ChartValidationError) as excinfo:
            OptionPriceValidator.validate(args, botfig)
        assert "Option has expired" in str(excinfo.value)

        args = ["C", "BTC", "22MAY22", "100"]
        with pytest.raises(ChartValidationError) as excinfo:
            OptionPriceValidator.validate(args, botfig)
        assert "Option has expired" in str(excinfo.value)

    def test_invalid_stirike_format(self, botfig: Botfig) -> None:
        expiry = generate_expiry_date("SOL")
        args = ["CS", "SOL", expiry, "strike1", "strike2"]
        with pytest.raises(ChartValidationError) as excinfo:
            OptionPriceValidator.validate(args, botfig)
        assert (
            "Invalid strikes provided. Please provide numerical strikes"
            in str(excinfo.value)
        )

    def test_valid_single_call(self, botfig: Botfig) -> None:
        expiry = generate_expiry_date("SOL")
        args = ["C", "SOL", expiry, "100"]
        result = OptionPriceValidator.validate(args, botfig)
        assert isinstance(result, ValidatedPriceRequest)
        assert result.op_type == "C"
        assert result.currency == "SOL"
        assert result.expiry == expiry
        assert result.strikes == [100]

    def test_valid_spread(self, botfig: Botfig) -> None:
        expiry = generate_expiry_date("SOL")
        args = ["CS", "SOL", expiry, "200", "250"]
        result = OptionPriceValidator.validate(args, botfig)
        assert isinstance(result, ValidatedPriceRequest)
        assert result.op_type == "CS"
        assert result.currency == "SOL"
        assert result.expiry == expiry
        assert result.strikes == [200, 250]

    def test_valid_risk_reversal(self, botfig: Botfig) -> None:
        expiry = generate_expiry_date("BTC")
        args = ["RR", "BTC", expiry, "100000", "105000"]
        result = OptionPriceValidator.validate(args, botfig)
        assert isinstance(result, ValidatedPriceRequest)
        assert result.op_type == "RR"
        assert result.currency == "BTC"
        assert result.expiry == expiry
        assert result.strikes == [100_000, 105_000]

    def test_valid_butterfly(self, botfig: Botfig) -> None:
        expiry = generate_expiry_date("ETH")
        args = ["BF", "ETH", expiry, "10000", "11000", "12000"]
        result = OptionPriceValidator.validate(args, botfig)
        assert isinstance(result, ValidatedPriceRequest)
        assert result.op_type == "BF"
        assert result.currency == "ETH"
        assert result.expiry == expiry
        assert result.strikes == [10_000, 11_000, 12_000]

    def test_expiry_beyond_max_tenor_cutoff(self, botfig: Botfig) -> None:
        # For each currency in CURRENCY_TO_MAX_PRICING_CUTOFF, test that an expiry beyond the cutoff is rejected
        for currency, max_cutoff in CURRENCY_TO_MAX_PRICING_CUTOFF.items():
            # Create a date that is beyond the cutoff
            future_date = (
                (datetime.now(UTC) + timedelta(days=max_cutoff + 30))
                .strftime("%d%b%y")
                .upper()
            )

            args = ["C", currency, future_date, "100"]
            with pytest.raises(ChartValidationError) as excinfo:
                OptionPriceValidator.validate(args, botfig)
            assert (
                f"Expiry {future_date} is beyond the maximum tenor cutoff of {max_cutoff} days for {currency}"
                in str(excinfo.value)
            )

    def test_expiry_within_max_tenor_cutoff(self, botfig: Botfig) -> None:
        # For each currency in CURRENCY_TO_MAX_PRICING_CUTOFF, test that an expiry within the cutoff is accepted
        for currency, max_cutoff in CURRENCY_TO_MAX_PRICING_CUTOFF.items():
            # Create a date that is within the cutoff
            valid_date = (
                (datetime.now(UTC) + timedelta(days=max_cutoff - 30))
                .strftime("%d%b%y")
                .upper()
            )

            args = ["C", currency, valid_date, "100"]
            result = OptionPriceValidator.validate(args, botfig)
            assert isinstance(result, ValidatedPriceRequest)
            assert result.currency == currency
            assert result.expiry == valid_date
