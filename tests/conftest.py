import pytest

from telegram_bot.configs.bot_config import Botfig
from telegram_bot.typings import ExchangeToCurrencies


@pytest.fixture
def funding_rate_config() -> ExchangeToCurrencies:
    return {"binance": ["BTC", "ETH", "BNB"], "bybit": ["BTC", "ETH"]}


@pytest.fixture
def spot_config() -> ExchangeToCurrencies:
    return {"coinbase": ["BTC"], "kraken": ["BTC", "ETH"]}


@pytest.fixture
def botfig(
    funding_rate_config: ExchangeToCurrencies, spot_config: ExchangeToCurrencies
) -> Botfig:
    return Botfig.model_validate(
        {
            "vol_charting_exchange": "v2composite",
            "internal_api_key": "",
            "is_premium_bot": False,
            "pricing_exchange": "v2composite",
            "currency_to_exchange_mapping": {
                "XRP": "deribit",
                "SUI": "coincall",
                "BNB": "coincall",
                "ARB": "blockscholes-syn",
                "OP": "blockscholes-syn",
            },
            "default_exchange": "v2composite",
            "supported_vol_chart_currencies": [
                "BTC",
                "ETH",
                "SOL",
                "XRP",
                "SUI",
                "BNB",
                "ARB",
                "OP",
            ],
            "supported_pricing_currencies": [
                "BTC",
                "ETH",
                "SOL",
                "XRP",
                "SUI",
                "BNB",
                "ARB",
                "OP",
            ],
            "timeseries_version": "",
            "funding_rate_config": funding_rate_config,
            "spot_config": spot_config,
            "whitelisted_users": [
                {"id": 123, "name": "test_user1"},
                {"id": 456, "name": "test_user2"},
            ],
        }
    )
