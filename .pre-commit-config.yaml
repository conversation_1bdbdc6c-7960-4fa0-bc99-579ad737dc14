# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v3.2.0
  hooks:
  -   id: trailing-whitespace
  -   id: end-of-file-fixer
  -   id: check-added-large-files
- repo: https://github.com/ambv/black
  rev: 24.1.1
  hooks:
  - id: black
    args: ["--check"]
    exclude: ^.*\b(migrations)\b.*$
- repo: https://github.com/astral-sh/ruff-pre-commit
  rev: v0.1.14
  hooks:
  - id: ruff
    exclude: ^.*\b(migrations)\b.*$
- repo: https://github.com/pre-commit/mirrors-mypy
  rev: v1.8.0  # Use the sha / tag you want to point at
  hooks:
  - id: mypy
