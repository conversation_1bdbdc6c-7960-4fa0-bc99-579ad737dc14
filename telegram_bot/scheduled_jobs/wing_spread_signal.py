import logging
from typing import Any

from telegram.ext import ContextTypes

from telegram_bot.configs.bot_config import Botfig
from telegram_bot.constants import PERMISSIONED_SIGNAL_CHAT_IDS
from telegram_bot.handlers.chart_builder import (
    build_wing_spread_signal_plot_objects,
)
from telegram_bot.handlers.signal import get_signal_charts_and_data
from telegram_bot.tg_bot_utils import get_wing_spread_threshold
from telegram_bot.typings import (
    ChartsAndData,
    ChartTimeseriesData,
    ValidatedSignalChartRequest,
)

_FLAGS_KEY = "wing_spread_flags"


def _extract_latest_values(data: ChartTimeseriesData) -> dict[str, float]:
    latest: dict[str, float] = {}
    for key, series in data.items():
        if not series:
            continue
        try:
            newest = max(series, key=lambda x: x.get("timestamp", 0) or 0)
            latest[key] = float(newest["value"])
        except Exception:
            continue
    return latest


def _build_crossing_message(
    crossed_above: list[str], crossed_below: list[str]
) -> str:
    lines: list[str] = ["Wing Spread Signal Update"]
    if crossed_above:
        lines.append("\nCrossed ABOVE threshold:")
        for s in crossed_above:
            lines.append(f" - {s}")
    if crossed_below:
        lines.append("\nDropped BELOW threshold:")
        for s in crossed_below:
            lines.append(f" - {s}")
    lines.append(
        "\nWe only send this chart when a signal crosses its configured threshold."
    )
    return "\n".join(lines)


async def _send_signal_to_chats(
    context: ContextTypes.DEFAULT_TYPE,
    image: Any,
    caption: str,
) -> None:
    for chat_id in PERMISSIONED_SIGNAL_CHAT_IDS:
        try:
            await context.bot.send_photo(
                chat_id=chat_id, photo=image, caption=caption
            )
        except Exception:
            # Best-effort; continue sending to others
            pass


async def process_scheduled_wing_spread_signal(
    context: ContextTypes.DEFAULT_TYPE,
    logger: logging.Logger,
    botfig: Botfig,
) -> None:

    signal_request = ValidatedSignalChartRequest(chart_type="wing spread")

    try:
        plot_objects = await build_wing_spread_signal_plot_objects()

        decoded_images_and_data: ChartsAndData = (
            await get_signal_charts_and_data(
                signal_request=signal_request,
                plot_objects=plot_objects,
                botfig=botfig,
                logger=logger,
            )
        )
    except Exception:
        logger.exception(
            "Failed to build wing spread scheduled signal charts/data"
        )
        return

    # Expect a single chart under v2timeseries
    payloads = decoded_images_and_data.get("v2timeseries", {})
    if not payloads:
        logger.error("No timeseries payloads found for wing spread signals")
        return

    # Pick the first (and only) chart payload
    chart_name, payload = next(iter(payloads.items()))
    data = payload.get("data")
    if not data:
        logger.error(
            "No data returned for wing spread signals; nothing to evaluate"
        )
        return

    latest_values = _extract_latest_values(data)

    # Load previous flags and compute crossings
    bot_flags = (
        context.bot_data.get(_FLAGS_KEY, {})
        if isinstance(context.bot_data, dict)
        else {}
    )
    crossed_above: list[str] = []
    crossed_below: list[str] = []

    new_flags: dict[str, bool] = dict(bot_flags)

    for key, value in latest_values.items():
        try:
            currency, delta_key, tenor = key.split("_")
        except ValueError:
            # Unexpected key format
            continue

        threshold = get_wing_spread_threshold(currency, delta_key, tenor)
        if threshold is None:
            # Skip unconfigured signals
            continue

        now_above = value >= threshold
        prev_above = bot_flags.get(key)

        if prev_above is None:
            # Initialize state without sending on first observation
            new_flags[key] = now_above
            continue

        if not prev_above and now_above:
            crossed_above.append(f"{currency} {delta_key} {tenor}")
        elif prev_above and not now_above:
            crossed_below.append(f"{currency} {delta_key} {tenor}")

        new_flags[key] = now_above

    # Persist updated flags
    context.bot_data[_FLAGS_KEY] = new_flags

    if not crossed_above and not crossed_below:
        logger.info(
            "No wing spread threshold crossings detected; nothing to send"
        )
        return

    # Send the combined chart image with a caption describing crossings
    caption = _build_crossing_message(crossed_above, crossed_below)
    await _send_signal_to_chats(
        context=context, image=payload["image"], caption=caption
    )

    logger.info(
        f"Sent wing spread signal chart '{chart_name}' for crossings: above={crossed_above}, below={crossed_below}"
    )
