import logging

from telegram.ext import ContextTypes

from telegram_bot.configs.bot_config import Botfig
from telegram_bot.handlers.chart_builder import (
    build_wing_spread_signal_plot_objects,
)
from telegram_bot.handlers.signal import get_signal_charts_and_data
from telegram_bot.typings import ValidatedSignalChartRequest


async def process_scheduled_wing_spread_signal(
    context: ContextTypes.DEFAULT_TYPE,
    logger: logging.Logger,
    botfig: Botfig,
) -> None:

    signal_request = ValidatedSignalChartRequest(chart_type="wing spread")

    try:
        plot_objects = await build_wing_spread_signal_plot_objects()

        decoded_images_and_data = await get_signal_charts_and_data(
            signal_request=signal_request,
            plot_objects=plot_objects,
            botfig=botfig,
            logger=logger,
        )

        _ = decoded_images_and_data

    except Exception:
        logger.exception(
            "Failed to build wing spread scheduled signal charts/data"
        )
        return

    pass
