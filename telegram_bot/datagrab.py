from datetime import UTC, datetime

import utils_general
from datagrabber import CatalogAssetType, get_instruments_async


async def grab_instruments(
    exchange: str, currency: str, asset_type: CatalogAssetType
) -> list[str]:
    instruments = await get_instruments_async(
        fields=[],
        start=utils_general.to_iso(datetime.now(tz=UTC)),
        end=utils_general.to_iso(datetime.now(tz=UTC)),
        exchanges=[exchange],
        asset_types=[asset_type],
        base_assets=[currency],
    )
    return [i["instrument"] for i in instruments]
