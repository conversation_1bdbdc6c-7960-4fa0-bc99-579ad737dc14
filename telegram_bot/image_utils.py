import io
from collections.abc import Mapping, Sequence
from typing import Any

import pandas as pd
from matplotlib import pyplot as plt
from matplotlib.transforms import Bbox


def build_table_image(
    rows: Sequence[Mapping[str, Any]] | pd.DataFrame,
    columns: Sequence[str] | None = None,
    *,
    # Styling parameters
    bg_color: str = "#101A2E",
    font_size: int = 16,
    fig_width: float = 16.0,
    row_height: float = 0.5,
    edge_color: str = "white",
    edge_linewidth: float = 0.5,
    header_text_color: str = "white",
    header_bold: bool = True,
    dpi: int = 150,
    # Color map for data rows: list mapping each row -> {column_name: color}
    data_text_color_map: Sequence[Mapping[str, Any]] | None = None,
) -> io.BytesIO:
    """
    Render a table image from provided rows and columns with shared styling.

    Parameters:
    - rows: list of dicts or a pandas DataFrame representing table rows.
    - columns: list of column names (required if rows is not a DataFrame).
    - bg_color: figure and cell background color.
    - font_size: table font size.
    - fig_width: figure width (in inches).
    - row_height: approximate height per row; figure height is computed as len(rows)*row_height + 1.
    - edge_color: table cell border color.
    - edge_linewidth: table cell border width.
    - header_text_color: text color for header row.
    - header_bold: whether header text is bold.
    - dpi: image DPI when saving.
    - data_text_color_map: optional per-row text color mapping for data rows; if provided,
      must have length equal to number of data rows. If None, defaults to header_text_color for all cells.

    Returns:
    - BytesIO containing the PNG image.
    """

    if font_size <= 0:
        raise ValueError("font_size must be positive")
    if fig_width <= 0:
        raise ValueError("fig_width must be positive")
    if row_height <= 0:
        raise ValueError("row_height must be positive")
    if edge_linewidth < 0:
        raise ValueError("edge_linewidth must be non-negative")
    if dpi <= 0:
        raise ValueError("dpi must be positive")

    # Normalize to DataFrame
    if isinstance(rows, pd.DataFrame):
        df = rows.copy()
        if columns is not None:
            df = df[columns]
    else:
        if columns is None:
            # infer columns from first row to maintain order if possible
            if not rows:
                columns = []
            else:
                columns = list(rows[0].keys())
        df = pd.DataFrame(list(rows), columns=list(columns))

    # Figure size based on number of rows
    n_rows = len(df.index)
    fig_height = n_rows * row_height + 1

    fig, ax = plt.subplots(figsize=(fig_width, fig_height))
    fig.patch.set_facecolor(bg_color)
    ax.axis("off")

    # Create a table that fills entire figure
    bbox = Bbox([[0, 0], [1, 1]])
    table = ax.table(
        cellText=df.astype(str).values.tolist(),
        colLabels=df.columns.tolist(),
        cellLoc="center",
        loc="center",
        bbox=bbox,
    )

    table.auto_set_font_size(False)
    table.set_fontsize(font_size)

    if data_text_color_map is not None and len(data_text_color_map) != n_rows:
        raise ValueError(
            f"data_text_color_map length ({len(data_text_color_map)}) "
            f"must equal number of data rows ({n_rows})"
        )

    # Style cells
    for i in range(n_rows + 1):  # +1 to include header
        for j, col in enumerate(df.columns):
            cell = table[(i, j)]
            cell.set_edgecolor(edge_color)
            cell.set_linewidth(edge_linewidth)
            cell.set_facecolor(bg_color)

            if i == 0:  # header row
                cell.get_text().set_color(header_text_color)
                if header_bold:
                    cell.get_text().set_fontweight("bold")
            else:
                if data_text_color_map is not None:
                    # color map index i-1 corresponds to data row i
                    color = data_text_color_map[i - 1].get(
                        col, header_text_color
                    )
                    cell.get_text().set_color(color)
                else:
                    cell.get_text().set_color(header_text_color)

    buf = io.BytesIO()
    plt.savefig(buf, format="png", bbox_inches="tight", dpi=dpi)
    buf.seek(0)
    plt.close(fig)
    return buf
