from datetime import UTC, datetime

from telegram_bot.constants import DEFAULT_WING_SPREAD_TARGETS
from telegram_bot.typings import WingSp<PERSON><PERSON><PERSON>


def expiry_formatter(date_string: str) -> str:
    """Format a date string to ISO 8601 format with additional adjustments.

    Args:
        date_string: The input date string (e.g., '01JAN23').

    Returns:
        A formatted ISO 8601 date string with the specific timezone adjustment.
    """

    # Parse the date and set time to 8 AM UTC with microseconds
    formatted_expiry = datetime.strptime(date_string, "%d%b%y").replace(
        hour=8, minute=0, second=0, microsecond=0, tzinfo=UTC
    )

    # Convert to ISO 8601 format with microseconds and 'Z' for UTC
    return str(formatted_expiry.strftime("%Y-%m-%dT%H:%M:%S.%f") + "Z")


def get_effective_wing_spread_targets() -> dict[WingSpreadKey, float]:
    return DEFAULT_WING_SPREAD_TARGETS


def get_wing_spread_threshold(
    currency: str,
    delta_key: str,
    tenor: str,
) -> float | None:
    """
    Lookup the threshold for a given (currency, delta_key, tenor) triplet.

    Returns:
        The threshold as float if found, otherwise None.
    """
    wing_spread_key = _construct_wing_spread_key(currency, delta_key, tenor)
    targets = get_effective_wing_spread_targets()
    return targets.get(wing_spread_key)


def _construct_wing_spread_key(
    currency: str,
    delta_key: str,
    tenor: str,
) -> WingSpreadKey:

    assert "delta" in delta_key, "Delta key must contain 'delta'"
    assert tenor.endswith("d"), "Tenor must end with 'd'"

    return (currency, delta_key, tenor)
