from pydantic import RootModel, model_validator

from telegram_bot.configs.aws_config import get_params
from telegram_bot.constants import (
    DEFAULT_EXCHANGE,
    DERIBIT_SUPPORTED_PRICE_CURRENCIES,
    OKX_SUPPORTED_PRICE_CURRENCIES,
    OKX_SUPPORTED_VOL_CHART_CURRENCIES,
)


class ExchangeMappingConfig(RootModel[dict[str, str]]):
    """
    Configuration for mapping currencies to exchanges.
    """

    @model_validator(mode="before")
    def ensure_default(cls, values: dict[str, str]) -> dict[str, str]:
        """
        Ensure every nested mapping has a default key that defaults to "v2composite".
        """
        if "" not in values:
            values[""] = DEFAULT_EXCHANGE
        return values


def get_currency_exchange_mapping() -> ExchangeMappingConfig:
    """
    Get exchange mapping configuration from SSM.

    Returns:
        ExchangeMappingConfig: The exchange mapping configuration
    """
    params = get_params("EXCHANGE_MAPPING")["option"]["blockscholes"]
    assert isinstance(params, dict)
    return ExchangeMappingConfig.model_validate(params)


def get_all_supported_currencies(
    exchange_mapping: dict[str, str],
) -> list[str]:
    """
    Get all supported currencies across different exchanges.

    Args:
        exchange_mapping: The exchange mapping configuration

    Returns:
        A list of all supported currencies
    """
    return list(
        {
            *[i for i in exchange_mapping.keys() if i],
            *OKX_SUPPORTED_VOL_CHART_CURRENCIES,
            *OKX_SUPPORTED_PRICE_CURRENCIES,
            *DERIBIT_SUPPORTED_PRICE_CURRENCIES,
        }
    )
