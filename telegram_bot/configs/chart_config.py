from telegram_bot.typings import (
    AllowedChartCommandTypes,
    PlotTypes,
    SignalChartCommandTypes,
)

# Chart type to plot type mapping
CHART_TYPE_TO_PLOT_TYPE: dict[
    AllowedChartCommandTypes | SignalChartCommandTypes, PlotTypes
] = {
    "vol": "v2timeseries",
    "smile": "v2smiles",
    "skew": "v2timeseries",
    "butterfly": "v2timeseries",
    "spot": "v2timeseries",
    "fr": "v2timeseries",
    "perp": "v2timeseries",
    "wing spread": "v2timeseries",
}

# Chart type to qualified name suffix mapping
CHART_TYPE_TO_QN_SUFFIX: dict[AllowedChartCommandTypes, str] = {
    "vol": "smile",
    "skew": "skew",
    "butterfly": "butterfly",
}

# Chart type to timeseries lookback mapping
CHART_TYPE_TO_TIMESERIES_LOOKBACK: dict[
    AllowedChartCommandTypes | SignalChartCommandTypes, int
] = {
    "fr": 7,
    "perp": 7,
    "spot": 7,
    "wing spread": 30,
}

# Tenor colors
DEFAULT_TENOR_COLOURS = {
    3: "#FFFFFF",
    7: "#247CFF",
    14: "#C5C5C5",
    30: "#FFCD00",
    60: "#FF54AF",
    90: "#E16100",
    # 120: "#c700ff",
    180: "#43FF64",
}

# Perpetual time series colors
PERPETUAL_TIME_SERIES_COLOURS = {
    "ETH": "#883CFF",
    "ETH_USDC": "#C49DFF",
    "BTC": "#FFCD00",
    "BTC_USDC": "#F4F4B0",
    "SOL": "#008B8B",
    "SOL_USDC": "#00CED1",
}
