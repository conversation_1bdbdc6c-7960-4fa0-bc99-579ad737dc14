"""
Configuration for analytics services like PostHog.
"""

from typing import Any

from posthog import Posthog

from telegram_bot.configs.aws_config import get_params


def _get_posthog_config() -> dict[str, Any]:
    """
    Get analytics configuration from SSM or environment variables.

    Returns:
        dict: Analytics configuration parameters
    """

    posthog_params = get_params("POSTHOG", by_path=False)
    return posthog_params


_posthog_config = _get_posthog_config()

posthog_client = Posthog(
    api_key=_posthog_config["posthog_api_key"],
    host=_posthog_config["posthog_host"],
)  # type: ignore[no-untyped-call]
