class ChartValidationError(Exception):
    pass


class PriceValidationError(Exception):
    pass


class OptionPricingTimestampError(Exception):
    pass


class ImageNotFoundError(Exception):
    pass


class DataNotFoundError(Exception):
    """Raised when expected data for a request cannot be found or is empty."""

    pass


class NoPriceArgumentsException(Exception):
    pass


class NoChartArgumentsException(Exception):
    pass


class NoVolRunArgumentsException(Exception):
    pass


class UnsupportedRunCurrencyException(Exception):
    pass


class PermissionDeniedException(Exception):
    pass


class RVDRequestConstructionError(Exception):
    pass


class LambdaResultDecodingError(Exception):
    pass


class MissingLambdaResponseError(Exception):
    pass
