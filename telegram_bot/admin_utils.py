import logging

from telegram import Update
from telegram.error import Forbidden
from telegram.ext import ContextTypes

from telegram_bot.constants import (
    BLOCKSCHOLES_TG_BOT_ADMIN_IDS,
)


# Handler to capture user IDs from unauthorized commands
async def handle_unrecognized_command(
    update: Update, context: ContextTypes.DEFAULT_TYPE, logger: logging.Logger
) -> None:
    user = update.effective_user

    message_text = update.message.text if update.message is not None else ""
    # Check if update.message is not None before accessing text
    if user:
        # Log the user information
        logger.info("Unrecognised command attempt from a user")

        message = (
            f"A user attempted to use an unrecognised command:\n"
            f"Command: {message_text}"
        )

        # Reply to the user
        try:
            if update.message is not None:
                await update.message.reply_text(
                    "Command not Recognised. Try using /help for a list of available commands."
                )
        except Forbidden:
            pass
    else:
        logger.info(
            "Received an unrecognised command without user information."
        )
        message = (
            "Potential bot trying to access command:\n"
            f"Command: {message_text}"
        )
    for admin_chat_id in BLOCKSCHOLES_TG_BOT_ADMIN_IDS:
        try:
            await context.bot.send_message(chat_id=admin_chat_id, text=message)
        except Exception as e:
            logger.error(f"Failed to contact Admin {admin_chat_id}: {e}")
