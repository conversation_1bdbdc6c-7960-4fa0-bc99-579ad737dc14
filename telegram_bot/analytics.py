"""
PostHog integration for tracking user commands and interactions.
"""

import logging
from typing import Any

from telegram import Update

from telegram_bot.configs.analytics_config import posthog_client
from telegram_bot.configs.aws_config import STAGE


def capture_command(
    update: Update,
    bot_id: str,
    command_name: str,
    command_args: dict[str, Any],
    logger: logging.Logger,
) -> None:
    """
    Capture a command execution in PostHog.

    Args:
        update: The Telegram update object
        context: The Telegram context
        command_name: The name of the command being executed
        command_args: Optional dictionary of command arguments
        logger: Optional logger instance
    """
    if update.effective_user is None:
        logger.warning(f"No effective user found for command: {command_name}")
        return

    user_id = update.effective_user.id

    properties = {
        "command": command_name,
        "user_id": user_id,
        "bot_id": bot_id,
    }
    properties.update(command_args)

    try:
        posthog_client.capture(  # type: ignore[no-untyped-call]
            distinct_id=str(user_id),
            event=f"TelegramBot.{STAGE}.command_executed",
            properties=properties,
        )
    finally:
        try:
            # By default the capture command sends events to a buffer where these
            # events are periodically flushed. Events can be lost if the application
            # shutsdown before a flush happens so we flush after every capture
            posthog_client.flush()  # type: ignore[no-untyped-call]

        except Exception as e:
            logger.error(f"PostHog flush failed: {e}")
