import asyncio
import logging
from datetime import (
    UTC,
    datetime,
)

import utils_general
from telegram import Update
from telegram.constants import ParseMode
from telegram.ext import ContextTypes

from telegram_bot.configs.bot_config import Botfig
from telegram_bot.configs.option_config import (
    OP_STYLE_MAP,
    OP_TYPE_MAP,
    OP_TYPE_VERBOSE_NAME_MAP,
)
from telegram_bot.constants import (
    COMMAND_DELAYS,
)
from telegram_bot.exceptions import (
    NoPriceArgumentsException,
    OptionPricingTimestampError,
    PriceValidationError,
)
from telegram_bot.handlers.messaging import (
    send_failed_request_message,
    send_message_suggestion,
)
from telegram_bot.handlers.option_pricer_utils import (
    price_option,
)
from telegram_bot.handlers.utils import (
    apply_time_delay_to_command,
    should_apply_delay,
)
from telegram_bot.handlers.validate import (
    OptionPriceValidator,
)
from telegram_bot.tg_bot_utils import (
    expiry_formatter,
)
from telegram_bot.typings import (
    AggregatedNetGreeksResponse,
    NetGreeksResponse,
    PricerRequestDict,
    Stage,
    ValidatedPriceRequest,
)


async def handle_price_request(
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    botfig: Botfig,
    aws_stage: Stage,
    logger: logging.Logger,
) -> None:
    """
    Handles the incoming option pricing request from a Telegram user.

    Validates the user's input, processes the option pricing request,
    invokes the appropriate AWS Lambda function to generate the pricing response,
    and sends the option pricing info back to the user.

    Parameters:
    - update (Update): Incoming Telegram update that triggered the handler.
    - context (ContextTypes.DEFAULT_TYPE): Context provided by the Telegram bot.
    - **kwargs (Any): Additional keyword arguments.

    Returns:
    - None
    """

    if update.effective_chat is None or update.effective_user is None:
        logger.error("Effective User not found")
        return

    try:

        price_request = _process_price_arguments(context, botfig=botfig)

    except NoPriceArgumentsException as _err:

        await send_message_suggestion(
            context=context,
            update=update,
            error=_err,
            command_type="price",
            botfig=botfig,
        )

        return

    except PriceValidationError as _err:

        logger.exception("Invalid message format encountered")
        await send_message_suggestion(
            context=context,
            update=update,
            error=_err,
            command_type="price",
            botfig=botfig,
        )

        return

    except Exception as _err:

        error_message = str(_err)
        logger.exception("Unexpected error occurred")
        await send_failed_request_message(
            context, update, error=error_message, command_type="price"
        )

        return

    try:

        req_confirmation_message_text: str = (
            f"Pricing: {price_request.currency} {price_request.expiry}\n"
            f"Strike(s): {', '.join(str(price) for price in (price_request.strikes))}\n"
            f"Option Type: {OP_TYPE_VERBOSE_NAME_MAP[price_request.op_type]}"
        )

        await context.bot.send_message(
            chat_id=update.effective_chat.id, text=req_confirmation_message_text
        )

        apply_delay = should_apply_delay(
            is_premium_bot=botfig.is_premium_bot,
            user_id=update.effective_user.id,
            chat_id=update.effective_chat.id,
            user_username=update.effective_user.username,
            user_whitelist=botfig.get_user_whitelist,
            chat_whitelist=botfig.get_chat_whitelist,
        )
        if apply_delay:
            price_timestamp = (
                apply_time_delay_to_command(
                    timestamp=datetime.now(UTC).replace(
                        minute=0, second=0, microsecond=0
                    ),
                    command_type="price",
                    apply_delay=apply_delay,
                ).strftime("%Y-%m-%dT%H:%M:%S.%f")
                + "Z"
            )
        else:
            # any bot that doesn't have a delay enabled does need to care about a whitelist and should return
            # the latest price
            price_timestamp = "LATEST"

        expiry = expiry_formatter(price_request.expiry)

        price_request.strikes = [float(i) for i in price_request.strikes]

        request_list: list[PricerRequestDict]
        request_list = generate_pricer_requests(price_request, expiry)

        try:
            pricer_response = (
                await handle_single_currency_option_pricer_request(
                    requests_list=request_list,
                    price_timestamp=price_timestamp,
                    botfig=botfig,
                    aws_stage=aws_stage,
                    logger=logger,
                )
            )
        except OptionPricingTimestampError as _err:
            message = "Failed to get price. Please contact an admin"
            await send_failed_request_message(
                context, update, error=message, command_type="price"
            )
            return

        except Exception as _err:
            logger.exception("Unexpected error occurred")
            await send_failed_request_message(
                context, update, error=str(_err), command_type="price"
            )
            return

        # Prepare the pricing response message if no error occurred
        coin_price = pricer_response.price / pricer_response.spot

        sign_of_price = "-" if pricer_response.price < 0 else ""

        # Will match input pricing timestamp, or will return the actual pricing timestamp if `LATEST` is specified
        price_timestamp_pretty = utils_general.to_datetime(
            pricer_response.pricing_timestamp
        ).strftime(  # Make it timezone-aware as UTC
            "%B %d, %Y %H:%M"
        )

        price_response_message_text: str = (
            f"<b>Price: {sign_of_price}${abs(pricer_response.price):.2f} ({coin_price:.3f} {price_request.currency})</b>\n\n"
            f"<i>Spot Ref: ${pricer_response.spot:.2f}</i>\n"
            f"<i>Delta: {pricer_response.delta:.2f}</i>"
        )
        if isinstance(pricer_response, NetGreeksResponse):
            price_response_message_text += (
                f"<i>\nIV: {pricer_response.implied_vol * 100:.2f}</i>%"
            )

        if apply_delay:
            price_response_message_text += (
                f"\n\nAll prices are delayed by {int(COMMAND_DELAYS['price'].total_seconds() // 3600)!s} hours ({price_timestamp_pretty}).\n"
                f"To access live pricing, please /contact a member of the BlockScholes team."
            )
        else:
            price_response_message_text += (
                f"\n\nLatest Pricing ({price_timestamp_pretty} UTC)."
            )

        # Send the message to the user, checking if effective_chat is not None
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=price_response_message_text,
            parse_mode=ParseMode.HTML,
        )

    except NotImplementedError:

        logger.exception("Invalid request received")

        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="Pricing Function Failed",
        )

        return

    return None


def generate_pricer_requests(
    price_request: ValidatedPriceRequest,
    expiry: str,
) -> list[PricerRequestDict]:
    """
    Generate a list of pricer requests based on the provided parameters.

    Args:
        price_request (dict[, any]): A dictionary with option type, currency, and strikes.
        expiry (str): The expiry date for the option.

    Returns:
        List[dict[, any]]: A list of dictionaries representing pricer requests.
    """

    op_type = OP_TYPE_MAP[price_request.op_type]
    op_style = OP_STYLE_MAP[price_request.op_type]

    if price_request.op_type in ["C", "P", "DC", "DP"]:

        request_list = [
            PricerRequestDict(
                currency=price_request.currency,
                strike=str(price_request.strikes[0]),
                expiry=expiry,
                quantity=1,
                style=op_style,
                op_type=op_type,
            )
        ]

        return request_list

    elif price_request.op_type in ["CS", "PS"]:
        request_list = [
            PricerRequestDict(
                currency=price_request.currency,
                strike=(
                    str(min(price_request.strikes))
                    if op_type == "C"
                    else str(max(price_request.strikes))
                ),
                expiry=expiry,
                quantity=1,
                style="vanilla",
                op_type=op_type,
            ),
            PricerRequestDict(
                currency=price_request.currency,
                strike=(
                    str(max(price_request.strikes))
                    if op_type == "C"
                    else str(min(price_request.strikes))
                ),
                expiry=expiry,
                quantity=-1,
                style="vanilla",
                op_type=op_type,
            ),
        ]

        return request_list

    elif price_request.op_type == "RR":

        request_list = [
            PricerRequestDict(
                currency=price_request.currency,
                strike=str(min(price_request.strikes)),
                expiry=expiry,
                quantity=-1,
                style="vanilla",
                op_type="P",
            ),
            PricerRequestDict(
                currency=price_request.currency,
                strike=str(max(price_request.strikes)),
                expiry=expiry,
                quantity=1,
                style="vanilla",
                op_type="C",
            ),
        ]
        return request_list

    elif price_request.op_type == "BF":

        request_list = [
            PricerRequestDict(
                currency=price_request.currency,
                strike=str(min(price_request.strikes)),
                expiry=expiry,
                quantity=1,
                style="vanilla",
                op_type="C",
            ),
            PricerRequestDict(
                currency=price_request.currency,
                strike=str(sorted(price_request.strikes)[1]),
                expiry=expiry,
                quantity=-2,
                style="vanilla",
                op_type="C",
            ),
            PricerRequestDict(
                currency=price_request.currency,
                strike=str(max(price_request.strikes)),
                expiry=expiry,
                quantity=1,
                style="vanilla",
                op_type="C",
            ),
        ]
        return request_list

    else:
        raise ValueError(f"Unsupported operation type: {price_request.op_type}")


def _process_price_arguments(
    context: ContextTypes.DEFAULT_TYPE,
    botfig: Botfig,
) -> ValidatedPriceRequest:
    """
    Processes and validates the option pricing arguments provided by the user.

    Parameters:
    - context (ContextTypes.DEFAULT_TYPE): The context containing user arguments.

    Returns:
    - PriceRequest: An object containing the validated price request parameters.

    Raises:
    - PriceValidationError: If the provided arguments are invalid.
    """
    args = context.args
    if not args or args is None:
        raise NoPriceArgumentsException("No price arguments provided")

    return OptionPriceValidator.validate(args=args, botfig=botfig)


async def handle_single_currency_option_pricer_request(
    requests_list: list[PricerRequestDict],
    price_timestamp: str,
    botfig: Botfig,
    aws_stage: Stage,
    logger: logging.Logger,
) -> AggregatedNetGreeksResponse | NetGreeksResponse:

    logger.info("Pricing response")
    tasks = [
        asyncio.create_task(
            price_option(
                req_params=request,
                aws_stage=aws_stage,
                botfig=botfig,
                value_timestamp=price_timestamp,
                logger=logger,
            )
        )
        for request in requests_list
    ]

    try:
        # Run all tasks in parallel
        results = await asyncio.gather(*tasks)
    except Exception as err:
        logger.error(f"Task failed with exception: {err}")
        raise Exception("Pricing request failed") from err

    # Validate results before aggregation
    if not all(isinstance(result, NetGreeksResponse) for result in results):
        raise ValueError("Invalid response format in results")

    try:
        net_greeks = await aggregate_combo_price_result(results, logger)
    except OptionPricingTimestampError as err:
        _error_message = "Error while aggregating combo pricing result"
        raise Exception(_error_message) from err
    except Exception as err:
        _error_message = "Error while aggregating combo pricing result"
        raise Exception(_error_message) from err

    return net_greeks


async def aggregate_combo_price_result(
    op_pricer_api_responses: list[NetGreeksResponse], logger: logging.Logger
) -> AggregatedNetGreeksResponse | NetGreeksResponse:

    if len(op_pricer_api_responses) == 1:
        return op_pricer_api_responses[0]
    return AggregatedNetGreeksResponse.aggregate_single_currency_position(
        op_pricer_api_responses, logger
    )
