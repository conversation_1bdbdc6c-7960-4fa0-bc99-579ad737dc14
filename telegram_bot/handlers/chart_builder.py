from datetime import UTC, datetime, timedelta
from typing import Any, Literal

import utils_general

from telegram_bot.configs.aws_config import RESEARCH_VOL_DASHBOARD_LAMBDA_NAME
from telegram_bot.configs.bot_config import Botfig
from telegram_bot.configs.chart_config import (
    CHART_TYPE_TO_QN_SUFFIX,
    CHART_TYPE_TO_TIMESERIES_LOOKBACK,
    DEFAULT_TENOR_COLOURS,
    PERPETUAL_TIME_SERIES_COLOURS,
)
from telegram_bot.constants import (
    CURRENCY_SMILE_DELAYS,
    DEFAULT_SMILE_DELAY,
    DEFAULT_WING_SPREAD_TARGETS,
    WING_SPREAD_CALC,
)
from telegram_bot.handlers.utils import (
    apply_time_delay_to_command,
    get_exchange_chart_name,
    is_constant_tenor,
    is_listed_expiry,
    parse_lookback_period,
)
from telegram_bot.typings import (
    AllowedCurrencies,
    ChartRequest,
    FlexSmilesTarget,
    FlexT<PERSON><PERSON>Chart,
    FlexTimeSeriesTarget,
    PlotObjects,
    RequestEvent,
    ValidatedFundingRateRequest,
    ValidatedPerpPriceRequest,
    ValidatedSignalChartRequest,
    ValidatedSmileRequest,
    ValidatedSpotRequest,
    ValidatedVolRequest,
    VolTimeseriesChartTypes,
)


async def build_smile_plot_objects(
    currency: AllowedCurrencies,
    chart_target: str,
    apply_delay: bool,
    botfig: Botfig,
) -> PlotObjects:
    """
    Builds the plot objects required for a 'smile' chart.

    Parameters:
    - currency (AllowedCurrencies): The currency code (e.g., 'BTC').
    - chart_target (str): The target tenor or expiry for the smile chart.
                          Use 'surface' to plot multiple smiles at the same snapshot.

    Returns:
    - PlotObjects: The plot objects for the smile chart.

    Raises:
    - NotImplementedError: If the tenor type is unrecognized.
    """

    target: FlexSmilesTarget

    exchange = botfig.get_exchange_for_currency(currency)

    # TODO: Remove default delay when LATEST keyword is handled in the RVD to use lookup_options
    smile_delay = CURRENCY_SMILE_DELAYS.get(currency, DEFAULT_SMILE_DELAY)
    smile_snapshot = utils_general.to_iso(
        apply_time_delay_to_command(
            timestamp=(
                datetime.now(UTC).replace(second=0, microsecond=0) - smile_delay
            ),
            command_type="chart",
            apply_delay=apply_delay,
        )
    )

    targets = []
    if chart_target.lower() == "surface":
        tenors = botfig.get_currency_tenor_target(currency)

        for tenor in tenors:
            target = {
                "tenor": int(tenor[:-1]),
                "exchange": exchange,
                "currency": currency.upper(),
                "model": "SVI",
                "snapshot": smile_snapshot,
                "trace_title": tenor,
                "color": DEFAULT_TENOR_COLOURS[int(tenor.replace("d", ""))],
            }
            targets.append(target)
    else:

        tgt: int | str = chart_target
        tenor_key: Literal["listed_expiry"] | Literal["tenor"]

        if is_listed_expiry(chart_target):
            tenor_key = "listed_expiry"
        elif is_constant_tenor(chart_target):
            tenor_key = "tenor"
            tgt = int(chart_target[:-1])
        else:
            raise NotImplementedError("Unrecognized tenor type")

        target = {
            tenor_key: tgt,  # type: ignore
            "exchange": exchange,
            "currency": currency.upper(),
            "model": "SVI",
            "snapshot": smile_snapshot,
            "trace_title": chart_target,
            "color": "#F7931A",
        }
        targets.append(target)

    plot_objects: PlotObjects = {
        "v2smiles": {
            "charts": {
                f"{currency.upper()} {chart_target} Smile.moneyness": {
                    "yaxis_title": "Volatility",
                    "chart_title": f"{get_exchange_chart_name(exchange)} {currency.upper()} SVI {chart_target.upper()} Volatility Smile -- {smile_snapshot}",
                    "targets": targets,
                }
            }
        }
    }

    return plot_objects


async def build_vol_based_timeseries_plot_objects(
    currency: AllowedCurrencies,
    chart_target: str,
    chart_type: VolTimeseriesChartTypes,
    botfig: Botfig,
) -> PlotObjects:
    """
    Builds the plot objects required for a 'vol' chart.

    Parameters:
    - currency (AllowedCurrencies): The currency code (e.g., 'BTC').
    - chart_target (str): The target strike or option parameter for the vol chart.

    Returns:
    - PlotObjects: The plot objects for the vol chart.
    """
    targets: list[FlexTimeSeriesTarget] = []

    tenors = botfig.get_currency_tenor_target(currency)
    exchange = botfig.get_exchange_for_currency(currency)

    for tenor in tenors:

        qualified_name = f"{exchange}.option.{currency.upper()}.SVI.{tenor}.1h.{CHART_TYPE_TO_QN_SUFFIX[chart_type]}"
        target: FlexTimeSeriesTarget = {
            "qualified_name": qualified_name,
            "trace_title": tenor,
            "color": DEFAULT_TENOR_COLOURS[int(tenor.replace("d", ""))],
            "target": chart_target,
        }
        targets.append(target)

    if chart_type == "vol":
        descriptor = "Volatility"
    else:
        descriptor = chart_type.capitalize()

    plot_objects: PlotObjects = {
        "v2timeseries": {
            "charts": {
                f"{currency.upper()} {chart_target.upper()} {descriptor}": {
                    "yaxis_title": descriptor,
                    "chart_title": f"{get_exchange_chart_name(exchange)} {currency.upper()} {chart_target.upper()} {descriptor}",
                    "targets": targets,
                }
            }
        }
    }

    return plot_objects


_TICK_SUB_QUERY_INTERVAL_SECONDS = 300


async def build_spot_price_plot_objects(
    instruments: list[str],
    exchange: str,
    currency: str,
) -> PlotObjects:
    """
    Builds the plot objects required for a 'vol' chart.

    Parameters:
    - currency (AllowedCurrencies): The currency code (e.g., 'BTC').

    Returns:
    - PlotObjects: The plot objects for the spot chart.
    """
    targets: list[FlexTimeSeriesTarget] = []

    for instr in instruments:
        # todo: move to traded price (tick.px) once the datasampling is enabled in prod
        qn = f"{exchange}.spot.{instr}.tick.bid.px"
        target: FlexTimeSeriesTarget = {
            "qualified_name": qn,
            "trace_title": instr,
            "sub_query_interval": _TICK_SUB_QUERY_INTERVAL_SECONDS,
            "resample_config": {
                "interval": "minute",
                "periods": int(_TICK_SUB_QUERY_INTERVAL_SECONDS / 60),
            },
            "target": "px",
        }

        if instr in PERPETUAL_TIME_SERIES_COLOURS:
            target.update({"color": PERPETUAL_TIME_SERIES_COLOURS[instr]})

        targets.append(target)

    descriptor = "Spot"
    exchange_name = get_exchange_chart_name(exchange)
    plot_objects: PlotObjects = {
        "v2timeseries": {
            "charts": {
                f"{exchange_name} {currency.upper()} {descriptor}": {
                    "yaxis_title": descriptor,
                    "chart_title": f"{exchange_name} {currency} {descriptor}",
                    "targets": targets,
                    "tickprefix": "$",
                }
            }
        }
    }

    return plot_objects


async def build_funding_rate_plot_objects(
    exchange: str, instruments: list[str], currency: str
) -> PlotObjects:
    """
    Builds the plot objects required for a 'vol' chart.

    Parameters:
    - currency (AllowedCurrencies): The currency code (e.g., 'BTC').

    Returns:
    - PlotObjects: The plot objects for the spot chart.
    """
    targets: list[FlexTimeSeriesTarget] = []

    for instrument in instruments:
        qn = f"{exchange}.perpetual.{instrument}.tick.funding.rate"
        target: FlexTimeSeriesTarget = {
            "qualified_name": qn,
            "trace_title": instrument,
            "target": "rate",
            "resample_config": {"interval": "minute", "periods": 1},
        }
        if instrument in PERPETUAL_TIME_SERIES_COLOURS:
            target.update({"color": PERPETUAL_TIME_SERIES_COLOURS[instrument]})

        targets.append(target)

    descriptor = "Funding Rate"
    exchange_name = get_exchange_chart_name(exchange)
    plot_objects: PlotObjects = {
        "v2timeseries": {
            "charts": {
                f"{exchange_name} {currency.upper()} {descriptor}": {
                    "yaxis_title": descriptor,
                    "chart_title": f"{exchange_name} {currency} {descriptor}",
                    "targets": targets,
                    "ticksuffix": "%",
                }
            }
        }
    }

    return plot_objects


async def build_perp_price_plot_objects(
    exchange: str, instruments: list[str], currency: str
) -> PlotObjects:
    """
    Builds the plot objects required for a 'vol' chart.

    Parameters:
    - currency (AllowedCurrencies): The currency code (e.g., 'BTC').

    Returns:
    - PlotObjects: The plot objects for the spot chart.
    """
    targets: list[FlexTimeSeriesTarget] = []

    for instrument in instruments:
        # todo: move to traded price (tick.px) once the datasampling is enabled in prod
        # todo: add special case handling for v2lyra when perp index price is ready
        qn = f"{exchange}.perpetual.{instrument}.tick.bid.px"
        target: FlexTimeSeriesTarget = {
            "qualified_name": qn,
            "trace_title": instrument,
            "target": "px",
            "sub_query_interval": _TICK_SUB_QUERY_INTERVAL_SECONDS,
            "resample_config": {
                "interval": "minute",
                "periods": int(_TICK_SUB_QUERY_INTERVAL_SECONDS / 60),
            },
        }
        if instrument in PERPETUAL_TIME_SERIES_COLOURS:
            target.update({"color": PERPETUAL_TIME_SERIES_COLOURS[instrument]})

        targets.append(target)

    descriptor = "Perp Price"
    exchange_name = get_exchange_chart_name(exchange)
    plot_objects: PlotObjects = {
        "v2timeseries": {
            "charts": {
                f"{exchange_name} {currency.upper()} {descriptor}": {
                    "yaxis_title": descriptor,
                    "chart_title": f"{exchange_name} {currency} {descriptor}",
                    "targets": targets,
                    "tickprefix": "$",
                }
            }
        }
    }

    return plot_objects


async def build_wing_spread_signal_plot_objects() -> PlotObjects:
    """
    Build a single timeseries chart that contains the requested delta targets,
    and one ATM target per unique (currency, tenor) pair (required for the
    WING_SPREAD_CALC computation).

    """

    unique_pairs = {(c, t) for (c, _d, t) in DEFAULT_WING_SPREAD_TARGETS.keys()}

    targets: list[FlexTimeSeriesTarget] = []
    for (
        currency,
        delta,
        tenor,
    ), _threshold in DEFAULT_WING_SPREAD_TARGETS.items():
        qname = f"v2composite.option.{currency}.SVI.{tenor}.1h.smile"
        targets.append(
            FlexTimeSeriesTarget(
                qualified_name=qname,
                target=delta,
            )
        )

    # Add ATM target for each unique pair - required for the calculation
    for currency, tenor in sorted(unique_pairs):
        qname = f"v2composite.option.{currency}.SVI.{tenor}.1h.smile"
        targets.append(
            FlexTimeSeriesTarget(
                qualified_name=qname,
                target="atm",
            )
        )

    chart_title = "Wing Spread Signals"
    charts: dict[str, FlexTimeseriesChart] = {
        chart_title: FlexTimeseriesChart(
            yaxis_title="Butterfly Spread",
            chart_title=chart_title,
            additional_lookback_days=15,
            include_latest_datapoints=True,
            # identifies to the lambda that this is a calculation chart
            calculation=WING_SPREAD_CALC,
            targets=targets,
        )
    }

    plot_objects: PlotObjects = {"v2timeseries": {"charts": charts}}

    return plot_objects


async def construct_rvd_chart_request(
    chart_request: ChartRequest | ValidatedSignalChartRequest,
    plot_objects: PlotObjects,
    exchange: str,
    apply_delay: bool,
    timeseries_lookback: int | None = None,
    version: str = "",
) -> dict[str, Any]:
    """
    Constructs the request payload for the AWS Lambda function.

    Parameters:
    - chart_request: The chart Request type
    - plot_objects (PlotObjects): The plot objects to include in the request.
    - exchange: The exchange to plot charts for
    - apply_delay: A boolean on whether or not a price delay should be applied to charting timestamp
    - timeseries_lookback (int, optional): Number of days to look back for timeseries data.
    - version (str, optional): Version of the timeseries data.

    Returns:
    - dict[str, Any]: The request payload for the Lambda function.

    Raises:
    - ValueError: If plot_objects is None.
    """
    if isinstance(chart_request, ChartRequest):
        currency = chart_request.currency
    else:
        # todo: remove currency from chart request types after fully migrating
        #  to "flex" charts
        # currency on the timeseries charts is redundant
        currency = ""

    if timeseries_lookback is None:
        timeseries_lookback = 30

    if plot_objects is None:
        raise ValueError("plot_objects must be provided.")

    if isinstance(
        chart_request,
        ValidatedSmileRequest
        | ValidatedSpotRequest
        | ValidatedFundingRateRequest
        | ValidatedPerpPriceRequest,
    ):
        # Use more reliable minutely data for smiles and term structure
        interval = "minute"
        end_date_tstamp = datetime.now(UTC).replace(second=0, microsecond=0)
    else:
        end_date_tstamp = datetime.now(UTC).replace(
            minute=0, second=0, microsecond=0
        )
        interval = "hour"

    # Determine command type based on chart request type
    command_type: Literal["chart", "signal"]
    if isinstance(chart_request, ValidatedSignalChartRequest):
        command_type = "signal"
    else:
        command_type = "chart"

    end_date = apply_time_delay_to_command(
        timestamp=end_date_tstamp,
        command_type=command_type,
        apply_delay=apply_delay,
    )
    start_date = end_date - timedelta(days=timeseries_lookback)

    start_date_str = utils_general.to_iso(start_date)
    end_date_str = utils_general.to_iso(end_date)

    # Build the request dict
    request = {
        "body": {
            "version": "1.0.0",
            "type": "ResearchVolDashboard",
            "calc": {
                "args": {
                    "debug": False,
                    "exchanges": [exchange],
                    "models": ["SVI"],
                    "currencies": [currency],
                    "frequency": {"interval": interval, "periods": 1},
                    # Used for timeseries requests - unused in smile requests
                    "date_range": {
                        "absolute": {
                            "start": start_date_str,
                            "end": end_date_str,
                        }
                    },
                    "plot_objects": plot_objects,
                },
                "output_options": {
                    "format": "timeseries",
                    "version": version,
                },
            },
        },
        "requestContext": {"stage": ""},
    }

    return request


async def build_rvd_request_event(
    *,
    name: str,
    chart_request: ChartRequest | ValidatedSignalChartRequest,
    plot_objects: PlotObjects,
    exchange: str,
    apply_delay: bool,
    timeseries_lookback: int | None,
    timeseries_version: str,
) -> RequestEvent:

    arguments = await construct_rvd_chart_request(
        chart_request=chart_request,
        plot_objects=plot_objects,
        version=timeseries_version,
        exchange=exchange,
        apply_delay=apply_delay,
        timeseries_lookback=timeseries_lookback,
    )

    return RequestEvent(
        lambda_name=RESEARCH_VOL_DASHBOARD_LAMBDA_NAME,
        name=name,
        arguments=arguments,
    )


def get_timeseries_lookback(
    chart_request: ChartRequest | ValidatedSignalChartRequest,
) -> int | None:
    """
    Determines the appropriate timeseries lookback period for a chart request.

    If the chart request has a custom lookback period (for ValidatedVolRequest),
    it calculates the days from that period. Otherwise, it uses the default
    lookback from the chart configuration.

    Parameters:
    - chart_request: The validated chart request

    Returns:
    - int | None: Number of days to look back, or None for default
    """
    # Check if this is a vol timeseries chart with a custom lookback period
    if (
        isinstance(chart_request, ValidatedVolRequest)
        and chart_request.lookback_period is not None
    ):

        return parse_lookback_period(chart_request.lookback_period)

    return CHART_TYPE_TO_TIMESERIES_LOOKBACK.get(chart_request.chart_type, None)
