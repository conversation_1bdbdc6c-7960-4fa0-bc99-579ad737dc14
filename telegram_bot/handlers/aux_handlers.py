import logging

from telegram import Update
from telegram.constants import Parse<PERSON><PERSON>
from telegram.ext import ContextTypes

from telegram_bot.configs.bot_config import Botfig
from telegram_bot.constants import (
    BLOCKSCHOLES_RESEARCH_GROUP_LINK,
    BL<PERSON><PERSON>CHOLES_RESEARCH_SIGNUP_LINK,
    BLOCKSCHOLES_TG_BOT_ADMIN_IDS,
    BLOCKSCHOLES_TG_BOT_CONTACT_REQUEST_GROUP_ID,
    COMMAND_DELAYS,
    PRIVACY_POLICY_LINK,
)
from telegram_bot.handlers.messaging import (
    get_chart_help_text,
    get_price_help_text,
    get_suggested_command_usage_msg,
    get_vol_run_usage_text,
)
from telegram_bot.handlers.utils import (
    should_apply_delay,
)


async def handle_start_command(
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    botfig: Botfig,
    logger: logging.Logger,
) -> None:
    """
    Sends a start message that introduces the bot and includes usage info
    for all handlers.
    """

    if update.effective_chat and update.effective_user:
        apply_delay = should_apply_delay(
            is_premium_bot=botfig.is_premium_bot,
            user_id=update.effective_user.id,
            chat_id=update.effective_chat.id,
            user_username=update.effective_user.username,
            user_whitelist=botfig.get_user_whitelist,
            chat_whitelist=botfig.get_chat_whitelist,
        )

        help_text = (
            "<b>Welcome to BlockScholes' Telegram Bot</b>, designed by options traders for options traders!\n\n"
            "<b>Get an instant quote for crypto options and combos:</b>\n"
            "/price\n\n"
            "<b>Analyse options market data:</b>\n"
            "/chart\n\n"
            "<b>Quickly compare volatility metrics:</b>\n"
            "/run\n\n"
            "<b>Help on using this bot:</b>\n"
            "/help\n\n"
            "<b><u>Premium Access:</u></b>\n"
            "• Live Options Pricing\n"
            "• Access to Latest Charts\n"
            "• Premium research channel access\n\n"
            "<b><u>Get in contact!</u></b>\n"
            "Any other questions?\n"
            "/contact a member of the BlockScholes team\n\n"
            "Interested in signing up?\n"
            "/signup to get Live access to our Bot and Premium Research"
            "\n\n<b><u>Join our community!</u></b>\n"
            f"Be part of the conversation with other options traders. Join our Telegram group: <a href='{BLOCKSCHOLES_RESEARCH_GROUP_LINK}'>@blockscholes</a>"
            f"\n\nBy using this bot, you agree to our <a href='{PRIVACY_POLICY_LINK}'>Privacy Policy</a>"
        )
        if apply_delay:
            help_text += (
                f"\n<i>Note: Pricing data is delayed by {int(COMMAND_DELAYS['price'].total_seconds() // 3600)!s} hours and "
                f"charts are delayed by {int(COMMAND_DELAYS['chart'].total_seconds() // 3600)!s} hours, and so may not reflect market conditions. </i>"
                "<i>To gain access to the full data, please reach out to our sales team.</i>"
            )
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=help_text,
            parse_mode=ParseMode.HTML,
        )


async def handle_help_command(
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    botfig: Botfig,
    logger: logging.Logger,
) -> None:
    """
    Sends an overall help message that includes usage info
    for all handlers.
    """
    logger.info("Handling /help command")

    if update.effective_chat:

        help_text = (
            f"{get_suggested_command_usage_msg()}\n\n"
            f"<b><u>Plotting Charts:</u></b>\n"
            f"Plot implied volatility data as a time series or visualise the latest available "
            f"market-implied volatility smile.\n\n"
            f"{get_chart_help_text(botfig)}\n\n"
            f"<b><u>Pricing Options:</u></b>\n"
            f"Calculate the market-implied price of an option or options combo / package.\n\n"
            f"{get_price_help_text(botfig)}\n\n"
            f"<b><u>Volatility Metrics:</u></b>\n"
            f"Quickly compare atm, risk-reversals, and butterfly across various tenors\n\n"
            f"{get_vol_run_usage_text(botfig)}\n"
            f"<b><u>Get in touch:</u></b>\n"
            f"Need more help, seen a bug, or would like to sign up to live pricing and charts?\n"
            f"/contact a member of the BlockScholes team.\n"
            "/signup to get Live access to our Bot and Premium Research"
        )

        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=help_text,
            parse_mode=ParseMode.HTML,
        )


async def handle_contact_command(
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    logger: logging.Logger,
) -> None:
    """
    Sends a message to the user indicating that a member of the BlockScholes team will
    be in touch shortly.
    """

    if update.effective_chat and update.effective_user:
        try:
            msg_to_blocks_scholes_internal_group_text = f"New /contact request from {update.effective_user.first_name} {update.effective_user.last_name} (@{update.effective_user.username}, {update.effective_user.id})"

            # Send message to notification channel - bots need to be inside this channel, otherwise an exception is raised
            await context.bot.send_message(
                chat_id=BLOCKSCHOLES_TG_BOT_CONTACT_REQUEST_GROUP_ID,
                text=msg_to_blocks_scholes_internal_group_text,
                parse_mode=ParseMode.HTML,
            )

        except Exception as _err:
            msg = "Error while sending notification to user signup channel"
            logger.exception(f"{msg}")
            for admin_chat_id in BLOCKSCHOLES_TG_BOT_ADMIN_IDS:
                try:
                    await context.bot.send_message(
                        chat_id=admin_chat_id,
                        text=f"{msg}, Error message={_err}",
                    )
                except Exception:
                    logger.exception(f"Failed to contact Admin {admin_chat_id}")

        msg_to_user_text = (
            "Thanks for reaching out!\n\n"
            "A member of the BlockScholes team will be in touch shortly."
        )

        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=msg_to_user_text,
            parse_mode=ParseMode.HTML,
        )


async def handle_signup_command(
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    logger: logging.Logger,
) -> None:
    """
    Sends a link to the research signup
    """

    logger.info("Handling /signup command")

    if update.effective_chat and update.effective_user:

        msg_to_user_text = (
            "Thanks for signing up!\n\n" f"{BLOCKSCHOLES_RESEARCH_SIGNUP_LINK}"
        )

        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=msg_to_user_text,
            parse_mode=ParseMode.HTML,
        )
