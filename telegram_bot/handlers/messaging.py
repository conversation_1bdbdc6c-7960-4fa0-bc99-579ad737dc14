from typing import get_args

from telegram import Update
from telegram.constants import ParseMode
from telegram.ext import ContextTypes

from telegram_bot.configs.bot_config import (
    Botfig,
)
from telegram_bot.exceptions import (
    NoChartArgumentsException,
    NoPriceArgumentsException,
    NoVolRunArgumentsException,
    PermissionDeniedException,
)
from telegram_bot.handlers.utils import generate_random_date_within_cutoff
from telegram_bot.typings import (
    AllowedDeltas,
    AllowedLookbackSuffixes,
    CommandType,
    VolTimeseriesChartTypes,
)


async def send_failed_request_message(
    context: ContextTypes.DEFAULT_TYPE,
    update: Update,
    error: str,
    command_type: CommandType,
) -> None:
    """
    Sends a message to the user indicating that the request failed.

    Parameters:
    - context (ContextTypes.DEFAULT_TYPE): The context from the Telegram bot.
    - update (Update): The update object representing the incoming message.
    - error (str): The error message to include.
    - command_type (str): The type of request that failed. (chart, price)

    Returns:
    - None
    """
    if not update.effective_chat or update.effective_chat is None:
        return

    command_to_error_msg: dict[CommandType, str] = {
        "chart": "Failed to get charts. Please contact the admin user. Error message={error}",
        "price": "Failed to get option price. Please contact the admin user. Error message={error}",
        "run": "Failed to produce Vol Run. Please contact the admin user. Error message={error}",
        "signal": "Failed to generate signal charts. Please contact the admin user. Error message={error}",
    }
    try:
        message_text = command_to_error_msg[command_type].format(error=error)
    except KeyError:
        raise NotImplementedError(
            f"Invalid request type: {command_type}"
        ) from None

    await context.bot.send_message(
        chat_id=update.effective_chat.id,
        text=message_text,
    )


def get_chart_usage_short_text() -> str:
    """
    Returns HTML-formatted usage text for /chart command.
    """
    # fmt: off
    text = (

        "<b>Volatility Timeseries:</b>\n"
        "<code>/chart vol BTC atm</code>\n"
        "<code>/chart skew XRP 25delta</code>\n"
        "<code>/chart butterfly BTC 45delta 3m</code> (3 month lookback)\n\n"

        "<b>Volatility Smiles:</b>\n"
        "<code>/chart smile BTC 30d</code>\n"
        "<code>/chart smile OP surface</code> (plots multiple tenors)\n\n"

        "<b>Spot Prices:</b>\n"
        "<code>/chart spot SUI kraken</code>\n"
        "<code>/chart spot XRP okx</code>\n\n"

        "<b>Perp Prices:</b>\n"
        "<code>/chart perp ADA bitmart</code>\n"
        "<code>/chart perp DOGE binance</code>\n\n"

        "<b>Funding rates:</b>\n"
        "<code>/chart fr FARTCOIN binance</code>\n"
        "<code>/chart fr ADA arbitrage</code> (cross exchange comparison)\n\n"

        "<i>For more details on charting, type </i>/help!"
    )
    # fmt: on
    return text


def get_chart_help_text(botfig: Botfig) -> str:
    """
    Returns HTML-formatted usage text for /chart command.
    """
    text = (
        # fmt: off
        "<b>Volatility Timeseries:</b>\n"
        "<code>/chart skew XRP 25delta</code>\n"
        "<code>/chart vol ARB atm</code>\n"
        "<code>/chart butterfly BTC 45delta ytd</code> (year-to-date)\n\n"

        "<i>Valid lookback periods (whitelisted users only):</i>\n"
        f"<i>{', '.join(get_args(AllowedLookbackSuffixes))}</i>\n"

        "<i>Valid chart types:</i>\n"
        f"<i>{', '.join(get_args(VolTimeseriesChartTypes))}</i>\n\n"

        "<i>Valid deltas (-ve puts, +ve calls):</i>\n"
        f"<i>{', '.join([delta.replace('delta', '') for delta in get_args(AllowedDeltas)])}</i>\n\n"

        "<b>Volatility Smiles:</b>\n"
        "<code>/chart smile SUI 30d</code>\n"
        "<code>/chart smile OP surface</code> -- plots multiple tenors\n\n"

        "<i>Valid currencies and their available constant tenors:</i>\n"
        f"<i>{botfig.display_allowed_constant_tenors}</i>\n\n"

        "<b>Spot:</b>\n"
        "<code>/chart spot SUI kraken</code>\n"
        "<code>/chart spot XRP okx</code>\n\n"

        "<i>Supported Spot Exchanges:</i>\n"
        f"<i>{', '.join(ex.capitalize() for ex in botfig.get_supported_exchanges_for_config_for_display('spot'))}</i>\n\n"

        "<b>Perp Prices:</b>\n"
        "<code>/chart perp ADA bitmart</code>\n"
        "<code>/chart perp DOGE binance</code>\n\n"

        "<b>Funding Rates:</b>\n"
        "<code>/chart fr ETH binance</code>\n"
        "<code>/chart fr 1000BONK bitget</code>\n"
        "<code>/chart fr BTC arbitrage</code> --- funding rates across exchanges\n\n"

        "<i>Supported Funding Rate Exchanges:</i>\n"
        f"<i>{', '.join(ex.capitalize() for ex in botfig.get_supported_exchanges_for_config_for_display('perpetual'))}</i>\n"
        # fmt: on
    )
    return text


def get_price_help_text(botfig: Botfig) -> str:
    """
    Returns HTML-formatted usage text for /help command.
    """
    btc_date = generate_random_date_within_cutoff(
        botfig.get_max_tenor_cutoff("BTC")
    )
    eth_date = generate_random_date_within_cutoff(
        botfig.get_max_tenor_cutoff("ETH")
    )
    sol_date = generate_random_date_within_cutoff(
        botfig.get_max_tenor_cutoff("SOL")
    )

    text = (
        # fmt: off
        "Arguments to the type of option are expected in the following order:\n"
        "/price &lt;option_type&gt; &lt;token&gt &lt;expiry&gt; &lt;strike&gt; ...\n\n"

        "<i>Valid option types and the number of strikes they expect:</i>\n"
        "<i>C: Call (1 strike)</i>\n"
        "<i>P: Put (1)</i>\n"
        "<i>DC: Digital Call (1)</i>\n"
        "<i>DP: Digital Put (1)</i>\n"
        "<i>CS: Call Spread (2)</i>\n"
        "<i>PS: Put Spread (2)</i>\n"
        "<i>RR: Risk Reversal (2)</i>\n"
        "<i>BF: Butterfly (3)</i>\n\n"

        f"<i>Valid tokens:\n{', '.join(botfig.supported_pricing_currencies)}</i>\n\n"

        "Expiries are expected in short-code form: DDMONYY. For example, December 31st, 2025 is expected as <code>'31DEC25'</code>. "
        "Options that have expired already cannot be priced. Each currency has a maximum tenor cutoff.\n\n"

        "<b>Examples</b><i>(click to copy to clipboard)</i>:\n"
        f"To get the price of a BTC call option with a strike of 100000 expiring on {btc_date}:\n"
        f"<code>/price C BTC {btc_date} 100000</code>\n\n"

        f"To get the price of a 3000/4000 ETH put spread expiring on {eth_date}:\n"
        f"<code>/price PS ETH {eth_date} 3000 4000</code>\n\n"

        f"To get the price of a 160/180/200 SOL butterfly spread expiring on {sol_date}:\n"
        f"<code>/price BF SOL {sol_date} 160 180 200</code>\n"
        # fmt: on
    )
    return text


def get_price_usage_short_text(botfig: Botfig) -> str:
    """
    Returns HTML-formatted usage text for /price command.
    """
    btc_date = generate_random_date_within_cutoff(
        botfig.get_max_tenor_cutoff("BTC")
    )

    eth_date = generate_random_date_within_cutoff(
        botfig.get_max_tenor_cutoff("ETH")
    )

    text = (
        # fmt: off
        "<b>To price an option, please input the following arguments:</b>\n"
        "<code>/price &lt;option_type&gt; &lt;token&gt; &lt;expiry&gt; &lt;strike&gt; ...</code>\n\n"

        "<b>Example (click to copy to clipboard):</b>\n"
        f"<code>/price C BTC {btc_date} 140000</code> (Call)\n\n"
        f"<code>/price DC BTC {btc_date} 140000</code> (Digital Call)\n\n"
        f"<code>/price PS ETH {eth_date} 3700 4300</code> (Put-Spread)\n\n"
        f"<code>/price RR ETH {eth_date} 4000 4300</code> (Risk-Reversal)\n\n"

        "<i>For more details on pricing options, type </i> /help!"
        # fmt: on
    )
    return text


def get_vol_run_usage_text(botfig: Botfig) -> str:
    """
    Returns HTML-formatted usage text for /run command.
    """
    text = (
        # fmt: off
        "<b>Vol Run</b>\n"
        "<code>/price &lt;currency&gt;</code>\n\n"

        "<b>Example (click to copy to clipboard):</b>\n"
        "<code>/run SUI</code>\n"
        "<code>/run XRP</code>\n\n"

        f"<i>Valid tokens:\n{', '.join(botfig.supported_pricing_currencies)}</i>\n\n"
        # fmt: on
    )
    return text


async def send_message_suggestion(
    context: ContextTypes.DEFAULT_TYPE,
    update: Update,
    error: Exception,
    command_type: CommandType,
    botfig: Botfig,
) -> None:
    """
    Sends a suggestion message to the user when the input format is invalid.

    Parameters:
        - context (ContextTypes.DEFAULT_TYPE): The context from the Telegram bot.
        - update (Update): The update object representing the incoming message.
        - error (str): The error message to include.
        - command_type (str): The type of request that failed. (chart, price)
        - botfig (Botfig): Bot specific configurations
    """
    if not update.effective_chat:
        return

    if command_type == "chart":
        if isinstance(error, NoChartArgumentsException):
            message_text = f"{get_suggested_command_usage_msg()}\n\n"
            message_text += f"{get_chart_usage_short_text()}"
        elif isinstance(error, PermissionDeniedException):
            message_text = f"{error!s}"
        else:
            message_text = (
                f"<b>Error</b>=<code>{error!s}</code>\n\n"
                f"{get_chart_usage_short_text()}"
            )

    elif command_type == "price":
        if isinstance(error, NoPriceArgumentsException):
            message_text = f"{get_suggested_command_usage_msg()}\n\n"
            message_text += f"{get_price_usage_short_text(botfig)}"
        else:
            message_text = (
                f"<b>Error</b>=<code>{error!s}</code>\n\n"
                f"{get_price_help_text(botfig)}"
            )
    elif command_type == "run":
        if isinstance(error, NoVolRunArgumentsException):
            message_text = f"{get_suggested_command_usage_msg()}\n\n"
            message_text += f"{get_vol_run_usage_text(botfig)}"
        else:
            message_text = (
                f"<b>Error</b>=<code>{error!s}</code>\n\n"
                f"{get_vol_run_usage_text(botfig)}"
            )
    else:
        raise ValueError(f"Invalid request type: {command_type}")

    await context.bot.send_message(
        chat_id=update.effective_chat.id,
        text=message_text,
        parse_mode=ParseMode.HTML,
    )


def get_suggested_command_usage_msg() -> str:
    return (
        "<b>ℹ️ Tap once on the command to copy</b><i> or type exactly as shown. "  # noqa: RUF001
        "Manual highlighting may not preserve formatting.</i>"
    )
