import logging
import time
from collections import deque

from telegram import Update
from telegram.ext import ApplicationHandlerStop, ContextTypes

from telegram_bot.analytics import capture_command
from telegram_bot.configs.aws_config import STAGE
from telegram_bot.constants import BLOCKSCHOLES_TG_BOT_ADMIN_IDS
from telegram_bot.logger import current_chat_id, current_user_id

_MAX_MESSAGES_IN_WINDOW = 7  # messages allowed in the evaluation window
_RATE_LIMIT_EVALUATION_WINDOW_SECONDS = 30  # time window to check for rate


async def rate_limit_callback(
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    logger: logging.Logger,
) -> None:

    if context.user_data is None or update.effective_message is None:
        logger.error(
            "User data or effective message is None. Ignoring rate limit check. user_data=%s, effective_message=%s",
            context.user_data,
            update.effective_message,
        )
        return

    user = update.effective_user
    if user is not None:
        if user.id in BLOCKSCHOLES_TG_BOT_ADMIN_IDS:
            return

    # Retrieve or initialize a deque to store timestamps for this user
    timestamps: deque[float] = context.user_data.get("timestamps", deque())
    context.user_data["timestamps"] = timestamps

    now = time.time()
    window = _RATE_LIMIT_EVALUATION_WINDOW_SECONDS

    # Remove timestamps that are older than the time window
    while timestamps and (now - timestamps[0] > window):
        timestamps.popleft()

    # If the user has exceeded the limit in this window, throttle them
    if len(timestamps) >= _MAX_MESSAGES_IN_WINDOW:
        # Determine how many messages need to expire before user is under limit
        messages_to_expire = len(timestamps) - int(_MAX_MESSAGES_IN_WINDOW) + 1

        # Identify the timestamp at which the necessary number of messages will expire
        # (since timestamps are ordered, the `messages_to_expire`-th oldest message will expire first)
        reference_time = list(timestamps)[
            messages_to_expire - 1
        ]  # Convert deque to list for indexing
        wait_until = reference_time + window
        wait_time = max(wait_until - now, 0)

        log_id: int | str
        if update.effective_user is not None:
            log_id = update.effective_user.id
        else:
            # other bots may not have id's
            log_id = "Unknown"

        logger.info(
            "User %s hit rate limit, must wait %.2f seconds",
            log_id,
            wait_time,
        )

        # Notify admins about this rate-limited event
        message_text = (
            update.message.text
            if update.message is not None
            else "<no message text>"
        )
        if update.effective_user:

            admin_message = (
                f"A User has been rate-limited.\n"
                f"Recent message: {message_text}\n"
                f"Required wait time: {wait_time:.1f} seconds."
            )
        else:

            admin_message = (
                f"A Potential bot has been rate-limited.\n"
                f"Recent message: {message_text}\n"
                f"Required wait time: {wait_time:.1f} seconds."
            )

        for admin_chat_id in BLOCKSCHOLES_TG_BOT_ADMIN_IDS:
            try:
                await context.bot.send_message(
                    chat_id=admin_chat_id, text=admin_message
                )
            except Exception as e:
                logger.error(
                    f"Failed to send rate limit alert to admin {admin_chat_id}: {e}"
                )

        await update.effective_message.reply_text(
            f"Rate limit exceeded. Please wait {wait_time:.1f} seconds."
        )
        raise ApplicationHandlerStop  # blocks other handlers from processing the update

    # If under the limit, record the current request timestamp and proceed
    timestamps.append(now)


async def capture_user_request_and_id_callback(
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    logger: logging.Logger,
    bot_id: str,
) -> None:
    """
    A special handler that runs first to set current_user_id and track command usage in PostHog.
    This callback has a group ID of -2, so it runs before all other handlers.
    """
    if update.effective_user:
        uid = update.effective_user.id
    else:
        # Tg user id's cannot be negative. Only chat id's can
        uid = -1
        logger.error(f"Failed set user. Defaulting to {uid}")

    # allows us to log chat id's so we can permission
    if update.effective_chat:
        chat_id = update.effective_chat.id
    else:
        chat_id = None
        logger.error("Failed to set chat id")

    current_user_id.set(uid)
    current_chat_id.set(chat_id)

    # Track command in PostHog if this is a command
    if (
        update.message
        and update.message.text
        and update.message.text.startswith("/")
    ):
        try:
            # Parse the command and arguments
            command_parts = update.message.text.split()
            command_name = command_parts[0].lstrip("/")
            command_args = command_parts[1:] if len(command_parts) > 1 else []

            # Create a dictionary of arguments with their positions
            args_dict = {
                "user_id": uid,
                "raw_command": f"{update.message.text}",
            }

            # Add each argument with its position
            for i, arg in enumerate(command_args):
                args_dict[f"arg_{i}"] = arg

            if STAGE != "local":
                # Track the command execution in PostHog
                capture_command(
                    update=update,
                    bot_id=bot_id,
                    command_name=command_name,
                    command_args=args_dict,
                    logger=logger,
                )

            # Log the command execution
            args_str = " ".join(command_args) if command_args else ""
            logger.info(
                f"Handling /{command_name} command with args: '{args_str}'"
            )

        except Exception as e:
            logger.error(f"Failed to track command: {e}")
