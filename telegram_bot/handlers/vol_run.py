import io
import logging
import time
from datetime import UTC, datetime, timedelta
from typing import Any, Literal, cast

import utils_general
from datagrabber import LookupOptions, construct_timeseries_queries, grab_async
from telegram import Update
from telegram.constants import ParseMode
from telegram.ext import ContextTypes

from telegram_bot.configs.bot_config import (
    Botfig,
)
from telegram_bot.constants import COMMAND_DELAYS
from telegram_bot.exceptions import (
    NoVolRunArgumentsException,
    UnsupportedRunCurrencyException,
)
from telegram_bot.handlers.messaging import (
    send_failed_request_message,
    send_message_suggestion,
)
from telegram_bot.handlers.utils import (
    apply_time_delay_to_command,
    should_apply_delay,
)
from telegram_bot.handlers.validate import VolRunValidator
from telegram_bot.image_utils import build_table_image
from telegram_bot.typings import (
    AllowedConstantTenors,
    CommandType,
    ValidatedVolRunRequest,
)


async def handle_run_request(
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    botfig: Botfig,
    logger: logging.Logger,
    **kwargs: Any,
) -> None:

    if update.effective_chat is None or update.effective_user is None:
        logger.error("Effective User not found")
        return

    apply_delay = should_apply_delay(
        is_premium_bot=botfig.is_premium_bot,
        user_id=update.effective_user.id,
        chat_id=update.effective_chat.id,
        user_username=update.effective_user.username,
        user_whitelist=botfig.get_user_whitelist,
        chat_whitelist=botfig.get_chat_whitelist,
    )

    try:
        vol_run_request = _process_run_arguments(context, botfig=botfig)
    except NoVolRunArgumentsException as _err:
        await send_message_suggestion(
            context=context,
            update=update,
            error=_err,
            command_type="run",
            botfig=botfig,
        )

        return

    except UnsupportedRunCurrencyException as _err:

        logger.exception("Invalid message format encountered")
        await send_message_suggestion(
            context=context,
            update=update,
            error=_err,
            command_type="run",
            botfig=botfig,
        )

        return

    await context.bot.send_message(
        chat_id=update.effective_chat.id,
        text=f"Generating vol run for {vol_run_request.currency}",
    )

    try:
        # Get the exchange for this currency from the mapping

        await _handle_vol_run_request_and_send_message(
            vol_run_request=vol_run_request,
            context=context,
            update=update,
            apply_delay=apply_delay,
            botfig=botfig,
        )
    except Exception as _err:
        error_message = str(_err)
        logger.exception("Error sending vol run table")
        await send_failed_request_message(
            context, update, error=error_message, command_type="run"
        )

    return


def _process_run_arguments(
    context: ContextTypes.DEFAULT_TYPE, botfig: Botfig
) -> ValidatedVolRunRequest:

    args = context.args
    if not args or args is None:
        raise NoVolRunArgumentsException("No arguments provided")

    return VolRunValidator.validate(args=args, botfig=botfig)


async def _handle_vol_run_request_and_send_message(
    vol_run_request: ValidatedVolRunRequest,
    context: ContextTypes.DEFAULT_TYPE,
    update: Update,
    apply_delay: bool,
    botfig: Botfig,
) -> None:

    if not update.effective_chat:
        return

    lookup_options: LookupOptions = {
        "order_by": {"pkey": "asc", "skey": "desc"},
        "limit": 1,
    }

    suffixes = ["skew", "butterfly", "smile"]
    timeseries_version = botfig.timeseries_version
    version_prefix = f"{timeseries_version}." if timeseries_version else ""
    snapshot = _get_snapshot_info(apply_delay, "run")

    qualified_names = _build_qualified_names(
        exchange=botfig.get_exchange_for_currency(vol_run_request.currency),
        currency=vol_run_request.currency,
        version_prefix=version_prefix,
        suffixes=suffixes,
        allowed_tenors=botfig.get_currency_tenor_target(
            vol_run_request.currency
        ),
    )
    recent_timestamp, recent_data = await _fetch_recent_snapshot(
        qualified_names=qualified_names,
        lookup_options=lookup_options,
        snapshot=snapshot,
    )

    ts_24h_ago = int(
        (
            utils_general.to_datetime(recent_timestamp) - timedelta(hours=24)
        ).timestamp()
        * 1e9
    )

    # Construct the timeseries queries for the 24h snapshot.
    queries_24h = construct_timeseries_queries(
        qualified_names=qualified_names,
        end=ts_24h_ago,
        fields=[],
        lookup_options=lookup_options,
    )
    data_24h_raw = await grab_async(queries_24h)

    current_table = _create_tenor_table(recent_data, recent_timestamp)
    table_24h = _create_tenor_table(data_24h_raw, ts_24h_ago)

    image_buffer = await create_table_image(current_table, table_24h)

    recent_timestamp_pretty = utils_general.to_datetime(
        recent_timestamp
    ).strftime("%B %d, %Y %H:%M")

    message_text = f"{vol_run_request.currency.upper()} Vol Run"

    if apply_delay:
        message_text += (
            f"\n\nAll vols are delayed by {int(COMMAND_DELAYS['run'].total_seconds() // 3600)!s} hours ({recent_timestamp_pretty}).\n"
            f"To access live data, please /contact a member of the BlockScholes team."
        )
    else:
        message_text += f"\n\nLatest Data ({recent_timestamp_pretty} UTC)."

    await context.bot.send_photo(
        chat_id=update.effective_chat.id,
        photo=image_buffer,
        caption=message_text,
        parse_mode=ParseMode.HTML,
    )

    return


async def _fetch_recent_snapshot(
    qualified_names: list[str],
    lookup_options: LookupOptions | None,
    snapshot: datetime | Literal["Latest"],
) -> tuple[int, list[dict[str, Any]]]:
    """
    Fetches the most recent snapshot using either a default time window (for "Latest")
    or an exact timestamp (for a delayed request).
    """
    if snapshot == "Latest":
        end_ns = int(time.time_ns())
    else:
        assert isinstance(snapshot, datetime)
        end_ns = int(snapshot.timestamp() * 1e9)

    queries = construct_timeseries_queries(
        qualified_names=qualified_names,
        end=end_ns,
        fields=[],
        lookup_options=lookup_options,
    )

    results = cast(list[dict[str, Any]], await grab_async(queries))
    if results and results[0].get("timestamp"):
        return int(results[0]["timestamp"]), results
    else:
        raise NotImplementedError("Unexpected error in fetching snapshot")


def _create_tenor_table(
    snapshot_data: list[dict[str, Any]], request_timestamp: int
) -> list[dict[str, Any]]:
    """
    Parses the snapshot data into a table (a list of dictionaries), one per tenor.
    For each record, based on the chart type:
      - For 'skew': extract the '25delta' value.
      - For 'butterfly': extract the '25delta' value.
      - For 'smile': extract the 'atm' value.
    The returned dictionary has keys: 'tenor', 'smile_atm', 'skew_25delta', and 'butterfly_25delta'.
    """
    table: dict[str, dict[str, Any]] = {}

    for record in snapshot_data:
        qualified_name = record.get("qualified_name", "")
        parts = qualified_name.split(".")
        tenor = parts[-3]
        chart_type = parts[-1].lower()
        if tenor not in table:
            table[tenor] = {
                "tenor": tenor,
                "request_timestamp": request_timestamp,
                "smile_atm": None,
                "skew_25delta": None,
                "butterfly_25delta": None,
            }
        if chart_type == "skew":
            table[tenor]["skew_25delta"] = round(record["25delta"] * 100, 2)
        elif chart_type == "butterfly":
            table[tenor]["butterfly_25delta"] = round(
                record["25delta"] * 100, 2
            )
        elif chart_type == "smile":
            table[tenor]["smile_atm"] = round(record["atm"] * 100, 2)
    return list(table.values())


def tenor_sort_key(tenor: str) -> float:
    """
    Extracts the numeric portion from a tenor (e.g., "1d" becomes 1.0) for sorting.
    """
    num_part = "".join(filter(str.isdigit, tenor))
    return float(num_part) if num_part else 0.0


async def create_table_image(
    current_table: list[dict[str, Any]], table_24h: list[dict[str, Any]]
) -> io.BytesIO:
    """
    Creates an image of a table displaying tenor data with 24h changes.

    Text color indicates value changes:
    - Green: increased values
    - Red: decreased values
    - White: unchanged values and headers

    Returns a BytesIO object containing the PNG image.
    """
    EPSILON = 0.0001
    BG_COLOR = "#101A2E"

    # Helper function to determine color based on change
    def get_color(diff: float) -> str:
        if diff > EPSILON:
            return "green"
        elif diff < -EPSILON:
            return "red"
        else:
            return "white"

    def get_sign(diff: float) -> str:
        return "+" if diff > 0 else ""

    current_by_tenor = {
        row["tenor"]: row for row in current_table if "tenor" in row
    }
    data24_by_tenor = {row["tenor"]: row for row in table_24h if "tenor" in row}
    all_tenors = sorted(
        set(current_by_tenor.keys()) | set(data24_by_tenor.keys()),
        key=tenor_sort_key,
    )

    # Build table data and track changes for coloring
    rows = []
    color_map = []

    for tenor in all_tenors:
        curr = current_by_tenor.get(tenor, {})
        prev = data24_by_tenor.get(tenor, {})

        # Calculate differences
        diff_atm = curr.get("smile_atm", 0) - prev.get("smile_atm", 0)
        diff_skew = curr.get("skew_25delta", 0) - prev.get("skew_25delta", 0)
        diff_butterfly = curr.get("butterfly_25delta", 0) - prev.get(
            "butterfly_25delta", 0
        )

        # Add row data
        rows.append(
            {
                "Tenor": tenor,
                "Atm % (24H Chg)": f"{curr.get('smile_atm', 'N/A')} ({get_sign(diff_atm)}{diff_atm:.1f})",
                "Skew % (24H Chg)": f"{curr.get('skew_25delta', 'N/A')} ({get_sign(diff_skew)}{diff_skew:.1f})",
                "Butterfly % (24H Chg)": f"{curr.get('butterfly_25delta', 'N/A')} ({get_sign(diff_butterfly)}{diff_butterfly:.1f})",
            }
        )

        # Track colors for each cell
        color_map.append(
            {
                "Tenor": "white",
                "Atm % (24H Chg)": get_color(diff_atm),
                "Skew % (24H Chg)": get_color(diff_skew),
                "Butterfly % (24H Chg)": get_color(diff_butterfly),
            }
        )

    # Use shared table builder with color map
    return build_table_image(
        rows,
        columns=[
            "Tenor",
            "Atm % (24H Chg)",
            "Skew % (24H Chg)",
            "Butterfly % (24H Chg)",
        ],
        bg_color=BG_COLOR,
        font_size=17,
        fig_width=16.0,
        row_height=0.6,
        edge_color="white",
        edge_linewidth=0.5,
        header_text_color="white",
        header_bold=True,
        dpi=150,
        data_text_color_map=color_map,
    )


def _get_snapshot_info(
    apply_delay: bool, command_type: CommandType
) -> datetime | Literal["Latest"]:
    """
    Returns a snapshot timestamp and lookup options based on whether delay is applied.
    If delay is applied, returns the delayed timestamp; otherwise returns "Latest" along with lookup options.
    """
    if not apply_delay:
        return "Latest"

    snapshot = apply_time_delay_to_command(
        timestamp=datetime.now(UTC).replace(second=0, microsecond=0),
        command_type=command_type,
        apply_delay=True,
    )
    return snapshot


def _build_qualified_names(
    exchange: str,
    currency: str,
    version_prefix: str,
    suffixes: list[str],
    allowed_tenors: list[AllowedConstantTenors],
) -> list[str]:
    """
    Constructs fully qualified query names for chart requests.
    """
    return [
        f"{version_prefix}{exchange}.option.{currency}.SVI.{tenor}.1m.{suff}"
        for suff in suffixes
        for tenor in allowed_tenors
    ]
