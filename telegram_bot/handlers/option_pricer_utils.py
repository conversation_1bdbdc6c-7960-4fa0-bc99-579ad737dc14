import logging
from typing import Literal, cast

import httpx

from telegram_bot.configs.bot_config import Botfig
from telegram_bot.typings import NetGreeksResponse, PricerRequestDict, Stage


async def price_option(
    req_params: PricerRequestDict,
    botfig: Botfig,
    aws_stage: Stage,
    value_timestamp: Literal["LATEST"] | str,
    logger: logging.Logger,
) -> NetGreeksResponse:
    """
    Calls the Option Pricer API to retrieve pricing data and greeks for an option.

    This function communicates with the Option Pricer API to get pricing details
    for a specified option. The function builds the required parameters, sends
    a GET request to the API endpoint, and processes the response.

    Args:
        req_params (dict[str, str | float | int]):
            A dictionary of request parameters required by the API. It must include:
            - "currency" (str): The currency code (e.g., BTC, ETH, XRP, SOL).
            - "strike" (str): The strike price of the option.
            - "expiry" (str): The expiry date of the option in ISO 8601 format.
            - "quantity" (float | int): The quantity of the option.
            - "style" (str): The option style (e.g., "vanilla" or "digital").
            - "type" (str): The option type (e.g., "C" for call, "P" for put).

        botfig (Botfig):
            The config object containing the API key and the exchange used for pricing

        value_timestamp (str | Literal["LATEST"]):
            Optional timestamp for pricing in ISO 8601 format. Defaults to None.

    Returns:
        NetGreeksResponse:
            A dictionary containing the pricing details of the option. The keys
            may include (but are not limited to):
            - "price" (float): The price of the option.
            - "implied vol" (float): The implied volatility.
            - "spot" (float): The current spot price.
            - "delta" (float): The delta value.
            - "gamma" (float): The gamma value.
            - "vega" (float): The vega value, scaled by 1/100.
            - "theta" (float): The theta value.
            - "volga" (float): The volga value.
            - "vanna" (float): The vanna value.
            - "pricing_timestamp" (int): The timestamp of the pricing data.
    """

    req_params_dict = req_params.model_dump()
    # Get the exchange for this currency from the mapping
    req_params_dict["exchange"] = botfig.get_exchange_for_currency(
        req_params.currency
    )
    req_params_dict["model"] = "SVI"
    req_params_dict["freq"] = "1m"
    # Option pricer api expects this as pricingTimestamp rather than pricing_timestamp
    req_params_dict["pricingTimestamp"] = value_timestamp

    # Option pricer API takes "type" as an argument, but cannot use "type" when specifying the type due to python key word
    req_params_dict["type"] = req_params_dict.pop("op_type")

    logger.info("Running call_api with parameters: %s", req_params_dict)

    # Make the request
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"https://{aws_stage}-api.blockscholes.com/api/option/pricer",
            params=req_params_dict,
            headers={
                "accept": "application/json",
                "x-api-key": botfig.internal_api_key,
            },
        )

    # handle case where response is not 200
    if response.status_code != 200:
        raise Exception("Option pricer response failed.")

    # Parse the JSON response
    try:
        response_dict = cast(NetGreeksResponse, response.json())

    except ValueError as exc:
        raise Exception(f"Failed to parse JSON response: {exc}") from exc

    # Ensure the response is a dict and matches the expected format
    if not isinstance(response_dict, dict):
        raise Exception(f"Unexpected response format: {response_dict}")

    response_dict["vega"] = response_dict["vega"] / 100

    return NetGreeksResponse.model_construct(
        spot=response_dict["spot"],
        pricing_timestamp=response_dict["pricing_timestamp"],
        implied_vol=response_dict["implied vol"],
        price=response_dict["price"],
        delta=response_dict["delta"],
        theta=response_dict["theta"],
        gamma=response_dict["gamma"],
        vega=response_dict["vega"],
    )
