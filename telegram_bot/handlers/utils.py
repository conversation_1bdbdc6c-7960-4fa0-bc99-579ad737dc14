import base64
import io
import random
import re
from datetime import UTC, datetime, timedelta
from typing import get_args

from telegram_bot.constants import (
    COMMAND_DELAYS,
    ONE_MONTH,
    ONE_YEAR,
)
from telegram_bot.typings import (
    AllowedLookbackSuffixes,
    ChartRequest,
    CommandType,
)


def decode_image(image_base64: str) -> io.BytesIO:
    image_bytes = base64.b64decode(image_base64)
    image_file = io.BytesIO(image_bytes)
    return image_file


def is_constant_tenor(user_input: str) -> bool:
    """
    Determine if the input is a relative date (e.g., '7d', '30d').

    Args:
        user_input (str): The input string to check.

    Returns:
        bool: True if the input is a relative date, False otherwise.
    """
    relative_pattern = r"^\d+[dD]$"
    return bool(re.match(relative_pattern, user_input))


def is_listed_expiry(user_input: str) -> bool:
    """
    Determine if the input is an absolute date (e.g., '23NOV24', '17DEC25').

    Args:
        user_input (str): The input string to check.

    Returns:
        bool: True if the input is an absolute date, False otherwise.
    """
    absolute_pattern = r"^\d{2}[A-Z]{3}\d{2}$"
    return bool(re.match(absolute_pattern, user_input))


def is_expired(user_input: str) -> bool:
    """
    Determine if the input expiry date is expired.

    Args:
        user_input (str): The input expiry date to check.

    Returns:
        bool: True if the input expiry date is expired, False otherwise.
    """
    date = datetime.strptime(user_input.upper(), "%d%b%y").replace(tzinfo=UTC)
    return datetime.now(UTC).date() >= date.date()


def get_exchange_chart_name(exchange: str) -> str:
    if exchange == "v2composite":
        return "BlockScholes"
    elif exchange == "v2lyra":
        return "Derive"

    return exchange.capitalize()


def apply_time_delay_to_command(
    timestamp: datetime,
    command_type: CommandType,
    apply_delay: bool,
) -> datetime:
    """
    Conditionally applies a delay to a timestamp.

    Parameters:
    - timestamp (datetime): The original timestamp.
    - apply_delay (bool): Flag indicating whether to apply the delay.
    - delay (timedelta): The delay duration to subtract from the timestamp if flagged.

    Returns:
    - datetime: The adjusted timestamp if apply_delay is True; otherwise, the original timestamp.
    """
    # todo: change to return LATEST when RVD can support it
    if apply_delay:
        return timestamp - COMMAND_DELAYS[command_type]
    return timestamp


def should_apply_delay(
    is_premium_bot: bool,
    user_id: int,
    chat_id: int,
    user_username: str | None,
    user_whitelist: list[int | str],
    chat_whitelist: list[int],
) -> bool:
    """
    Determine whether to apply a delay for a given user based on bot settings and whitelists.

    Args:
        is_premium_bot (bool): Global flag indicating if the bot is premium.
        user_id (int): The ID of the user requesting the action.
        chat_id (int): The ID of the chat where the action is requested.
        user_username (str | None): The username of the user requesting the action, if available.
        whitelist (list[int | str]): A list of user IDs, chat IDs, and usernames who are exempt from delay.

    Returns:
        bool: True if delays should be applied (i.e., the bot is not premium and the user is not in either whitelist),
              otherwise False.
    """

    return (
        not is_premium_bot
        and (user_id not in user_whitelist)
        and (user_username not in user_whitelist if user_username else True)
        and (chat_id not in chat_whitelist)
    )


def get_chart_request_key(chart_request: ChartRequest) -> str:
    if hasattr(chart_request, "chart_target"):
        return f"{chart_request.currency} {chart_request.chart_type} {chart_request.chart_target}"
    else:
        return f"{chart_request.currency} {chart_request.chart_type}"


def is_beyond_max_tenor_cutoff(expiry: str, max_cutoff_days: int) -> bool:
    """
    Determine if the input expiry date is beyond the maximum tenor cutoff.

    Args:
        expiry: The expiry date to check in format 'DDMMMYY' (e.g., '31DEC25')
        max_cutoff_days: The maximum tenor cutoff in days

    Returns:
        True if the expiry date is beyond the maximum tenor cutoff, False otherwise
    """
    expiry_date = datetime.strptime(expiry.upper(), "%d%b%y").replace(
        tzinfo=UTC
    )
    max_date = datetime.now(UTC) + timedelta(days=max_cutoff_days)
    return expiry_date.date() > max_date.date()


def generate_random_date_within_cutoff(max_cutoff_days: int) -> str:
    """
    Generate a random date within the maximum tenor cutoff for a given currency.

    Args:
        currency: The currency to generate a date for
        max_cutoff_days: The maximum tenor cutoff in days

    Returns:
        A random date within the tenor cutoff in format 'DDMMMYY' (e.g., '31DEC25')
    """
    # Generate a random number of days between 7 days and max_cutoff_days
    random_days = random.randint(7, max_cutoff_days)
    future_date = datetime.now(UTC) + timedelta(days=random_days)
    return future_date.strftime("%d%b%y").upper()


def parse_lookback_period(lookback_str: AllowedLookbackSuffixes) -> int:
    """
    Parse a lookback period string and return the number of days to look back.

    Args:
        lookback_str: The lookback period string (e.g., "3m", "1y", "ytd")

    Returns:
        int: Number of days to look back

    Raises:
        ValueError: If the lookback period is invalid
    """

    if lookback_str == "ytd":
        current_date = datetime.now(UTC)
        start_of_year = datetime(current_date.year, 1, 1, tzinfo=UTC)
        return (current_date - start_of_year).days

    # Define multipliers for different periods
    multipliers: dict[AllowedLookbackSuffixes, int] = {
        "1m": ONE_MONTH,
        "2m": 2 * ONE_MONTH,
        "3m": 3 * ONE_MONTH,
        "6m": 6 * ONE_MONTH,
        "1y": ONE_YEAR,
        "2y": 2 * ONE_YEAR,
    }

    if lookback_str in multipliers:
        return int(multipliers[lookback_str])

    raise ValueError(f"Invalid lookback period: {lookback_str}")


def is_valid_lookback_suffix(suffix: str) -> bool:
    """
    Check if a string is a valid lookback suffix.

    Args:
        suffix: The suffix to validate

    Returns:
        bool: True if valid, False otherwise
    """
    return suffix in get_args(AllowedLookbackSuffixes)
