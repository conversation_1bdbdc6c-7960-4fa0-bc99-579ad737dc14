import contextvars
import logging

# A context variable to store user_id across an async context or thread
current_user_id: contextvars.ContextVar[int | None] = contextvars.ContextVar(
    "current_user_id", default=None
)
current_chat_id: contextvars.ContextVar[int | None] = contextvars.ContextVar(
    "current_chat_id", default=None
)


class BotIdFilter(logging.Filter):
    def __init__(self, bot_id: str):
        super().__init__()
        self.bot_id = bot_id

    def filter(self, record: logging.LogRecord) -> bool:
        """Inject bot_id and user_id into the log record."""
        record.bot_id = self.bot_id

        # Grab user_id from the context var, if set
        uid = current_user_id.get()
        record.user_id = uid if uid else None

        # Grab chat_id from the context var, if set
        chat_id = current_chat_id.get()
        record.chat_id = chat_id if chat_id else None
        return True


class BotLogger:
    """
    A lightweight wrapper to create a per-bot logger without
    modifying any global/root logger configuration.
    """

    def __init__(self, bot_id: str, level: int = logging.INFO) -> None:
        self.bot_id = bot_id

        # 1) Create or retrieve a logger with a unique name
        self.logger = logging.getLogger(f"{bot_id}")

        # 2) Prevent log propagation to the root logger
        self.logger.propagate = False

        # 3) Create a handler (e.g. StreamHandler for console logs)
        handler = logging.StreamHandler()
        handler.setLevel(level)

        # 4) Define a custom formatter just for this bot's logs
        formatter = logging.Formatter(
            "%(asctime)s [%(levelname)s:%(process)d] - [BotID: %(bot_id)s] [ChatID: %(chat_id)s] [UserID: %(user_id)s] %(message)s"
        )
        handler.setFormatter(formatter)

        # 5) Attach a filter that injects bot_id & user_id
        handler.addFilter(BotIdFilter(bot_id))

        # 6) Add the handler to the logger
        self.logger.addHandler(handler)
        self.logger.setLevel(level)

    def get_logger(self) -> logging.Logger:
        """Returns the underlying logger instance."""
        return self.logger
